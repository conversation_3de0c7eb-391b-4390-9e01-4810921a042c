import { useLocaleContext } from "../context/LocaleContext";
import { useTableContext } from "../context/TableContext";
import { langs } from "../locale";

function LecturersRow({ data }) {
  const { locale } = useLocaleContext();
  const { pageInfo, relationFields } = useTableContext();
  const related = ["academic_degree", "directions_id"];

  const AffiliatedField = ({ field }) =>
    data[field] == 0 ? (
      <div className="badge badge-light-danger fw-bolder">
        {locale && langs[locale]["invited"]}
      </div>
    ) : data[field] == 1 ? (
      <div className="badge badge-light-success fw-bolder">
        {locale && langs[locale]["academic"]}
      </div>
    ) : (
      <div className="badge badge-light-secondary fw-bolder">
        -
      </div>
    );

  const RegularField = ({ field }) =>
    data[field] &&
    (related.includes(field) ? data[field]["name_ka"] : data[field]);

  return pageInfo.includeFields.map((field) => (
    <td key={field}>
      {field === "photo" ? (
        <img
          src={`${process.env.NEXT_PUBLIC_STORAGE}${data[field]}`}
          className="table__row__img"
          onError={(e) => (e.target.src = "/icons/user.png")}
        />
      ) : field === "academic_degree_id" ? (
        relationFields && relationFields["academic_degree"] && relationFields["academic_degree"].options ?
        relationFields["academic_degree"].options[data[field]] : data[field]
      ) : field !== "affiliated" ? (
        <RegularField field={field} />
      ) : (
        <AffiliatedField field={field} />
      )}
    </td>
  ));
}

export default LecturersRow;
