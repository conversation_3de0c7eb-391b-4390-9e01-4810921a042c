import { createContext, useContext, useEffect, useState } from "react";
import { langs } from "./../locale";
import { useLocaleContext } from "./LocaleContext";
import { getFinanceStatements } from "./../../helpers/funcs";

const Context = createContext();

import { useRouter } from "next/router";

import apiClientProtected from "../../helpers/apiClient";

import { studentsFields } from "../input_fields/studentsFields";
import { campusesFields } from "../input_fields/campusesFields";
import { schoolsFields } from "../input_fields/schoolsFields";
import { studentsProgramsFields } from "../input_fields/studentsProgramsFields";
import { programsFields } from "../input_fields/programsFields";
import { administrationFields } from "../input_fields/administrationFields";
import { roleFields } from "../input_fields/roleFields";
import { libraryFields } from "../input_fields/libraryFields";
import { librarySubjectFields } from "../input_fields/librarySubjectFields";
import { permissionsFields } from "../input_fields/permissionsFields";
import { studentGroupsFields } from "../input_fields/studentGroupsFields";
import { learYearsFields } from "../input_fields/learYearsFields";
import { lecturersFields } from "../input_fields/lecturersFields";
import { flowsFields } from "../input_fields/flowsFields";
import { administrationPositionsFields } from "../input_fields/administrationPositionsFields";
import { administrationItemsFields } from "../input_fields/administrationItemsFields";
import { auditoriumsFields } from "../input_fields/auditoriumsFields";
import { assessmentsFields } from "../input_fields/assessmentsFields";
import { newsFields } from "../input_fields/newsFields";
import { docsFields } from "../input_fields/docsFields";
import { minorLogsFields } from "../input_fields/minorLogsFields";

import * as constants from "./constants";

export function TableContext({ children }) {
  const { locale } = useLocaleContext();
  const [pageInfo, setPageInfo] = useState(null);
  const [paginationSettings, setPaginationSettings] = useState({});
  const [data, setData] = useState(null);
  const [campusesList, setCampusesList] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [modalType, setModalType] = useState("");
  const [errors, setErrors] = useState(null);
  const [tableData, setTableData] = useState(null);
  const [relationFields, setRelationFields] = useState({});
  const [shrinkSidebar, setShrinkSidebar] = useState(false);
  const [shrinkSidebarOnHover, setShrinkSidebarOnHover] = useState(false);
  const [booksRow, setBooksRow] = useState({});
  const [filterFields, setFilterFields] = useState({});
  const [filterBadges, setFilterBadges] = useState([]);
  const [flowId, setFlowId] = useState("");
  const [programId, setProgramId] = useState("");
  const [surveyId, setSurveyId] = useState("");
  const [questionId, setQuestionId] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [groupId, setGroupId] = useState("");
  const [statusId, setStatusId] = useState("");
  const [periods, setPeriods] = useState("");
  const [search, setSearch] = useState("");
  const [academicDegreeId, setAcademicDegreeId] = useState("");
  const [exportParams, setExportParams] = useState("");
  const [alertMessage, setAlertMessage] = useState({
    isOpen: false,
    title: "",
  });
  const [signsDataUpdate, setSignsDataUpdate] = useState(false);
  const [dataId, setDataId] = useState("");

  const router = useRouter();

  const { PageWithForm } = router.query;
  // console.log(router.query, "From outside use effect");

  const handleDataSubmit = (d) => {
    setData((prev) => [d, ...prev]);
  };

  const handleSyllabusEdit = (id, type) => {
    const syllabi = [...data];
    const filtered = syllabi.map((item) => {
      if (item.id === id && type === "update") {
        item.curriculum.lecture = null;
      } else if (item.id === id && type === "create") {
        item.curriculum = { lecture: {} };
      }
      return item;
    });

    setData(filtered);
  };

  const handleDataEdit = (d, type) => {
    let edited = null;
    // console.log(d, type); return
    if (type) {
      edited = data.find((item) => item.id === d.id && item.type === d.type);
    } else {
      edited = data.find((item) => item.id === d.id);
    }

    if (Object.entries(edited).length) {
      setErrors([]);

      Object.keys(edited).forEach((ed) => {
        if (typeof edited[ed] !== "object") {
          edited[ed] = d[ed];
        }
      });

      const editedIndex = data.indexOf(edited);
      const filtered = [...data];

      filtered[editedIndex] = d;

      setData(filtered);
    } else {
      setErrors(d);
    }
  };
  const handleDataDeletion = (id) => {
    setData(data.filter((dt) => dt.id !== id));
  };

  // useEffect(() => {

  // }, []);

  useEffect(() => {
    const hh = async () => {
      if (!PageWithForm || PageWithForm === "/finances") {
        return;
      }

      //console.log(filterFields, "FILTER FIELDS");

      let queryString = "?";
      for (let key in filterFields) {
        if (filterFields[key]) {
          queryString += `${key}=${filterFields[key]}&`;
        }
      }
      queryString = queryString.slice(0, queryString.length - 1);

      if (apiClientProtected()) {
        const response = await apiClientProtected().get(
          `${PageWithForm}${queryString}`
        );

        if (response.data.hasOwnProperty("data")) {
          setData((prevState) => response.data.data);
        } else if (response.data.hasOwnProperty(PageWithForm)) {
          setData((prevState) => response.data[PageWithForm].data);
        } else {
          setData((prevState) => response.data);
        }
        router.push(`${PageWithForm}${queryString}`);
      }
    };

    // hh();
  }, [filterFields]);

  const handleFilters = (field, id, value) => {
    // console.log(field, id, value, 'filters')
    let badgesArray = [...filterBadges];
    // console.log(badgesArray)
    if (badgesArray.find((item) => item.field === field)) {
      let item = badgesArray.find((item) => item.field === field);
      const index = badgesArray.indexOf(item);

      if (index !== -1) {
        badgesArray[index] = { id, field, title: value };
        setFilterBadges([...badgesArray]);
      }
      // console.log(field, value, id, 'SHmaidi')
    } else {
      setFilterBadges([...filterBadges, { id, field, title: value }]);
    }

    setFilterFields((prevState) => ({ ...prevState, [field]: id }));

    // For minor-logs page, update flowId when flows filter is changed
    if (PageWithForm === "minor-logs" && field === "flows") {
      setFlowId(id);
    }
  };

  const handleFilterDelete = (id, field) => {
    // console.log(id)
    let badgesArray = [...filterBadges];
    let filtersObject = { ...filterFields };
    badgesArray = badgesArray.filter((item) => item.field !== field);
    delete filtersObject[field];

    // console.log(filtersObject,  'Filters object')

    setFilterBadges(badgesArray);
    setFilterFields((prevState) => ({ ...filtersObject }));

    // For minor-logs page, clear flowId when flows filter is removed
    if (PageWithForm === "minor-logs" && field === "flows") {
      setFlowId("");
    }
  };

  useEffect(() => {
    setData(null);
    setErrors(null);
    setRelationFields({});
    setPageInfo(null);
    setSearch("");
    setFlowId("");
    setCampusesList(null);
    setTableData(null);
    if (Object.entries(filterFields).length) {
      setFilterFields({});
      setFilterBadges([]);
    }

    const params = window.location.search;
    // console.log(params);

    if (
      PageWithForm === "finances" ||
      PageWithForm === "curriculum" ||
      PageWithForm === "calendar" ||
      router.query.rowId
    ) {
      setShrinkSidebar(true);
      setShrinkSidebarOnHover(true);
    } else {
      setShrinkSidebar(false);
      setShrinkSidebarOnHover(false);
    }

    if (PageWithForm) {
      const pageObj = {
        title: "",
        modalTitle: "",
        columns: [],
        fetchLink: "",
        fields: [],
        routeName: "",
        includeFields: [],
        needsFilter: true,
        needsExport: true,
      };

      if (!constants.pages.includes(PageWithForm)) {
        router.push("/admin/404");
        return;
      }

      switch (PageWithForm) {
        case "students":
          pageObj.title = "students";
          pageObj.modalTitle = "სტუდენტის დამატება";
          pageObj.columns = constants.studentColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_STUDENTS + params;
          pageObj.fields = studentsFields;
          pageObj.routeName = "students";
          pageObj.includeFields = [
            "photo",
            "surname",
            "name",
            "personal_id",
            "email",
            "program",
            "student_group",
            "status",
            "gpa",
            "learn_year",
          ];
          break;

        case "bachelor":
          pageObj.title = "bachelor";
          pageObj.modalTitle = "აპლიკანტების დამატება";
          pageObj.columns = constants.applicantColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_REGISTER_BACHELORS;
          pageObj.fields = studentsFields;
          pageObj.needsFilter = false;
          pageObj.routeName = "bachelor";
          pageObj.includeFields = [
            "checkbox",
            "photo",
            "last_name",
            "first_name",
            "email",
            "date_of_birth",
            "identity_number",
            "phone",
            "program_id",
          ];
          break;
        case "master":
          pageObj.title = "master";
          pageObj.modalTitle = "აპლიკანტების დამატება";
          pageObj.columns = constants.applicantColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_REGISTER_MASTERS;
          pageObj.fields = studentsFields;
          pageObj.needsFilter = false;
          pageObj.routeName = "master";
          pageObj.includeFields = [
            "checkbox",
            "photo",
            "last_name",
            "first_name",
            "email",
            "date_of_birth",
            "identity_number",
            "phone",
            "program_id",
          ];
          break;
        case "phd":
          pageObj.title = "phd";
          pageObj.modalTitle = "აპლიკანტების დამატება";
          pageObj.columns = constants.applicantColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_REGISTER_DOCTORS;
          pageObj.fields = studentsFields;
          pageObj.needsFilter = false;
          pageObj.routeName = "phd";
          pageObj.includeFields = [
            "checkbox",
            "photo",
            "last_name",
            "first_name",
            "email",
            "date_of_birth",
            "identity_number",
            "phone",
            "program_id",
          ];
          break;
        case "tcc":
          pageObj.title = "tcc";
          pageObj.modalTitle = "აპლიკანტების დამატება";
          pageObj.columns = constants.applicantColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_REGISTER_TRAINING;
          pageObj.fields = studentsFields;
          pageObj.needsFilter = false;
          pageObj.routeName = "tcc";
          pageObj.includeFields = [
            "checkbox",
            "photo",
            "last_name",
            "first_name",
            "email",
            "date_of_birth",
            "identity_number",
            "phone",
            "program_id",
          ];
          break;
        case "hse":
          pageObj.title = "hse";
          pageObj.modalTitle = "აპლიკანტების დამატება";
          pageObj.columns = constants.applicantColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_REGISTER_PROFESSION;
          pageObj.fields = studentsFields;
          pageObj.needsFilter = false;
          pageObj.routeName = "hse";
          pageObj.includeFields = [
            "checkbox",
            "photo",
            "last_name",
            "first_name",
            "email",
            "date_of_birth",
            "identity_number",
            "phone",
            "program_id",
          ];
          break;
        case "lecturers":
          pageObj.title = "lecturers";
          pageObj.modalTitle = locale && langs[locale]["lecturer"];
          pageObj.columns = constants.lecturersColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_LECTURERS + params;
          pageObj.fields = lecturersFields;
          pageObj.routeName = "lecturers";
          pageObj.includeFields = [
            "photo",
            "last_name",
            "first_name",
            "phone",
            "email",
            "academic_degree",
            "affiliated",
          ];
          break;

        case "schools":
          pageObj.title = "schools";
          pageObj.modalTitle = "სკოლის დამატება";
          pageObj.columns = constants.schoolColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_SCHOOLS + params;
          pageObj.fields = schoolsFields;
          pageObj.routeName = "schools";
          pageObj.includeFields = ["name_ka", "name_en", "campus"];
          break;

        case "campuses":
          pageObj.title = "campuses";
          pageObj.modalTitle = "კამპუსის დამატება";
          pageObj.columns = constants.campusColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_CAMPUSES + params;
          pageObj.fields = campusesFields;
          pageObj.routeName = "campuses";
          pageObj.needsExport = false;
          pageObj.needsFilter = false;
          pageObj.includeFields = [
            "name_ka",
            "name_en",
            "address_ka",
            "address_en",
          ];
          break;

        case "student-groups":
          pageObj.title = "student_groups";
          pageObj.modalTitle = "სტუდენტური ჯგუფის დამატება";
          pageObj.columns = constants.studentGroupsColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_STUDENT_GROUPS;
          pageObj.fields = studentGroupsFields;
          pageObj.routeName = "student-groups";
          pageObj.needsExport = false;
          pageObj.includeFields = ["name_ka", "name_en", "program"];
          break;

        case "programs":
          pageObj.title = "learning_programs";
          pageObj.modalTitle = "პროგრამის დამატება";
          pageObj.columns = constants.programColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_PROGRAMS;
          pageObj.fields = programsFields;
          pageObj.routeName = "programs";
          pageObj.includeFields = [
            "name_ka",
            "name_en",
            "school",
            "academic_degree",
          ];
          break;

        case "administrations":
          pageObj.title = "administration";
          pageObj.modalTitle = "ადმინისტრაციის დამატება";
          pageObj.columns = constants.administrationColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_ADMINISTRATIONS + params;
          pageObj.fields = administrationFields;
          pageObj.routeName = "administrations";
          pageObj.includeFields = [
            "photo",
            "last_name",
            "first_name",
            "identity_number",
            "phone",
            "email",
            "cv",
            "administration_position",
          ];
          break;

        case "administration-positions":
          pageObj.title = "admin_positions";
          pageObj.modalTitle = "ადმინისტრაციის პოზიციის დამატება";
          pageObj.columns = constants.administrationPositionColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_ADMINISTRATION_POSITIONS;
          pageObj.fields = administrationPositionsFields;
          pageObj.routeName = "administration-positions";
          pageObj.needsExport = false;
          pageObj.needsFilter = false;
          pageObj.includeFields = ["name_ka", "name_en"];
          break;

        case "administration-items":
          pageObj.title = "admin_units";
          pageObj.modalTitle = "ადმინისტრაციის ერთეულის დამატება";
          pageObj.columns = constants.administrationItemsColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_ADMINISTRATION_ITEMS;
          pageObj.fields = administrationItemsFields;
          pageObj.routeName = "administration-items";
          pageObj.needsExport = false;
          pageObj.needsFilter = false;
          pageObj.includeFields = ["name_ka", "name_en"];
          break;

        case "learn-years":
          pageObj.title = "academic_years";
          pageObj.modalTitle = "სასწავლო წლის დამატება";
          pageObj.columns = constants.learnYearsColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_LEARN_YEARS;
          pageObj.fields = learYearsFields;
          pageObj.routeName = "learn-years";
          pageObj.includeFields = ["name", "program_id"];
          break;

        case "flows":
          pageObj.title = "flows";
          pageObj.modalTitle = "ნაკადის დამატება";
          pageObj.columns = constants.flowColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_FLOWS;
          pageObj.fields = flowsFields;
          pageObj.routeName = "flows";
          pageObj.needsExport = false;
          pageObj.includeFields = ["name", "program", "price", "url"];
          break;

        case "auditoriums":
          pageObj.title = "learning_auditors";
          pageObj.modalTitle = "სასწავლო აუდიტორის დამატება";
          pageObj.columns = constants.auditoriumColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_AUDITORIUMS;
          pageObj.fields = auditoriumsFields;
          pageObj.routeName = "auditoriums";
          pageObj.includeFields = ["name", "quantity"];
          break;
        case "roles":
          pageObj.title = "roles";
          pageObj.modalTitle = "როლის დამატება";
          pageObj.columns = constants.roleColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_ROLES;
          pageObj.fields = roleFields;
          pageObj.routeName = "roles";
          pageObj.includeFields = ["title", "users"];
          break;
        case "permissions":
          pageObj.title = "permission";
          pageObj.modalTitle = "ნებართვის დამატება";
          pageObj.columns = constants.permissionColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_PERMISSIONS;
          pageObj.fields = permissionsFields;
          pageObj.routeName = "permissions";
          pageObj.includeFields = ["title"];
          break;
        case "library-lmb":
          pageObj.title = "library-lmb";
          pageObj.modalTitle = "წიგნის დამატება";
          pageObj.columns = constants.libraryColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_LIBRARY;
          pageObj.fields = libraryFields;
          pageObj.routeName = "library-lmb";
          pageObj.includeFields = [
            "title",
            "author",
            "files",
            "lecturer",
            "subject",
            "published_date",
          ];
          break;
        case "library-subject":
          pageObj.title = "topic";
          pageObj.modalTitle = "თემატიკის დამატება";
          pageObj.columns = constants.librarySubjectColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_LIBRARY_SUBJECT;
          pageObj.fields = librarySubjectFields;
          pageObj.routeName = "library-subject";
          pageObj.includeFields = ["title"];
          break;
        case "assessments":
          pageObj.title = "eval_components";
          pageObj.modalTitle = "შეფასების დამატება";
          pageObj.columns = constants.assessmentsColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_ASSESSMENTS;
          pageObj.fields = assessmentsFields;
          pageObj.routeName = "assessments";
          pageObj.includeFields = ["name_ka", "name_en", "type_id"];
          break;
        case "finances":
          pageObj.title = "finances";
          pageObj.modalTitle = "ფინანსების დამატება";
          pageObj.columns = constants.flowColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_FINANCES;
          pageObj.routeName = "finances";
          pageObj.needsExport = false;
          pageObj.includeFields = ["name", "program"];
          break;
        case "finance-statement":
          pageObj.title = "Finance Statements";
          pageObj.modalTitle = "ფინანსური განცხადებები";
          pageObj.columns = constants.statementColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_FINANCE_STATEMENT;
          pageObj.routeName = "finance-statement";
          pageObj.needsExport = false;
          pageObj.includeFields = [
            "student",
            "personal_id",
            "program",
            "created_at",
            "status_id",
          ];
          break;
        case "curriculum":
          pageObj.title = "curriculum";
          pageObj.modalTitle = "კოპირება";
          pageObj.columns = constants.CURRICULUM_COLUMNS;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_CURRICULUM + params;
          pageObj.fields = librarySubjectFields;
          pageObj.routeName = "curriculum";
          pageObj.needsFilter = false;
          pageObj.includeFields = ["title"];
          break;
        case "calendar":
          pageObj.title = "calendar";
          pageObj.modalTitle = "ივენთის დამატება";
          pageObj.fetchLink = process.env.NEXT_PUBLIC_CALENDAR + params;
          pageObj.routeName = "calendar";
          break;
        case "journal":
          pageObj.title = "ac_performance";
          pageObj.modalTitle = "უწყისის დამატება";
          pageObj.columns = constants.journalColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_JOURNAL;
          pageObj.routeName = "journal";
          pageObj.needsFilter = true;
          pageObj.needsExport = false;
          pageObj.includeFields = [
            "code",
            "name",
            "lecturers",
            "studentGroups",
            "semester",
            "students_count",
          ];
          break;
        case "academic":
          pageObj.title = "academic_personal";
          pageObj.modalTitle = "წევრის დამატება";
          pageObj.columns = constants.lecturersColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_HR_ACADEMIC;
          pageObj.fields = lecturersFields;
          pageObj.routeName = "academic";
          pageObj.includeFields = [
            "photo",
            "last_name",
            "first_name",
            "phone",
            "email",
            "academic_degree_id",
            "affiliated",
          ];
          break;
        case "invited":
          pageObj.title = "invited_personal";
          pageObj.modalTitle = "წევრის დამატება";
          pageObj.columns = constants.lecturersColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_HR_INVITED;
          pageObj.fields = lecturersFields;
          pageObj.routeName = "invited";
          pageObj.includeFields = [
            "photo",
            "last_name",
            "first_name",
            "phone",
            "email",
            "academic_degree_id",
            "affiliated",
          ];
          break;
        case "administration":
          pageObj.title = "administration_personal";
          pageObj.modalTitle = "წევრის დამატება";
          pageObj.columns = constants.administrationColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_HR_ADMIN;
          pageObj.fields = administrationFields;
          pageObj.routeName = "administration";
          pageObj.includeFields = [
            "photo",
            "last_name",
            "first_name",
            "identity_number",
            "phone",
            "email",
            "cv",
            "administration_position",
          ];
          break;
        case "news":
          pageObj.title = "news";
          pageObj.modalTitle = "სიახლის დამატება";
          pageObj.columns = constants.newsColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_NEWS;
          pageObj.fields = newsFields;
          pageObj.routeName = "news";
          pageObj.includeFields = ["image", "title", "description"];
          break;
        case "signs":
          pageObj.title = "signs";
          pageObj.modalTitle = "ნიშნები";
          pageObj.columns = constants.newsColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_NEWS;
          pageObj.fields = newsFields;
          pageObj.routeName = "signs";
          pageObj.includeFields = [];
          break;
        case "edoc":
          pageObj.title = "e_doc";
          pageObj.modalTitle = "შაბლონის დამატება";
          pageObj.columns = constants.edocsColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_EDOCS;
          pageObj.fields = docsFields;
          pageObj.routeName = "edoc";
          pageObj.needsExport = false;
          pageObj.includeFields = [
            "name",
            "text",
            "index",
            "automatic",
            "status",
          ];
          break;
        case "edoc-inbox":
          pageObj.title = "e_docs";
          pageObj.modalTitle = "შაბლონის დამატება";
          pageObj.columns = constants.edocsInboxColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_EDOCS_INBOX;
          pageObj.fields = docsFields;
          pageObj.routeName = "edoc-inbox";
          pageObj.includeFields = [
            "user",
            "template",
            "text",
            "document_number",
            "created",
          ];
          break;
        case "edoc-sent":
          pageObj.title = "sent";
          pageObj.modalTitle = "შაბლონის დამატება";
          pageObj.columns = constants.edocsInboxColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_EDOCS_SENT;
          pageObj.fields = docsFields;
          pageObj.routeName = "edoc-sent";
          pageObj.includeFields = [
            "user",
            "template",
            "text",
            "document_number",
            "created",
          ];
          break;
        case "surveys":
          pageObj.title = "surveys";
          pageObj.modalTitle = "გამოკითხვის დამატება";
          pageObj.columns = constants.quizColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_QUIZ;
          pageObj.routeName = "surveys";
          pageObj.needsFilter = false;
          pageObj.needsExport = false;
          pageObj.includeFields = ["name", "description"];
          break;

        case "grade-analyze":
          pageObj.title = "grade_analyze";
          pageObj.modalTitle = "ნიშნების ანალიზი";
          pageObj.columns = constants.applicantColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_NEWS;
          pageObj.fields = studentsFields;
          pageObj.needsFilter = false;
          pageObj.routeName = "grade-analyze";
          pageObj.includeFields = [
            "checkbox",
            "photo",
            "last_name",
            "first_name",
            "email",
            "date_of_birth",
            "identity_number",
            "phone",
            "program_id",
          ];
          break;

        case "lecturer-finances":
          pageObj.title = "lecturer-finances";
          pageObj.modalTitle = "ჰონორარი";
          pageObj.columns = constants.applicantColumns;
          pageObj.fetchLink = process.env.NEXT_PUBLIC_NEWS;
          pageObj.fields = studentsFields;
          pageObj.needsFilter = false;
          pageObj.routeName = "lecturer-finances";
          pageObj.includeFields = [
            "checkbox",
            "photo",
            "last_name",
            "first_name",
            "email",
            "date_of_birth",
            "identity_number",
            "phone",
            "program_id",
          ];
          break;
        // http://127.0.0.1:8000/api/excel/lecturerFinance?date_from=2023-09-04&date_to=2023-09-05&program_id=&school_id

        case "minor-logs":
          pageObj.title = "minor-logs";
          pageObj.modalTitle = "მაინორის ლოგები";
          pageObj.columns = [
            { name: "სახელი", sort_key: "first_name" },
            { name: "გვარი", sort_key: "last_name" },
            { name: "პირადი ნომერი", sort_key: "personal_id" },
            { name: "მაინორის დასახელება", sort_key: "minor_id" },
            { name: "სასწავლო სემესტრი", sort_key: "flow_id" },
            { name: "თარიღი", sort_key: "created_at" },
          ];
          pageObj.fetchLink = process.env.NEXT_PUBLIC_MINOR_LOGS + params;
          pageObj.fields = minorLogsFields;
          pageObj.routeName = "minor-logs";
          pageObj.needsFilter = true;
          pageObj.needsExport = true;
          pageObj.includeFields = [
            "first_name",
            "last_name",
            "personal_id",
            "minor_name",
            "flow_name",
            "created_at",
          ];
          break;

        default:
          break;
      }

      setPageInfo(pageObj);
    }
  }, [PageWithForm]);

  useEffect(() => {
    if (PageWithForm === "finances" || PageWithForm === "curriculum") {
      return;
    }
    apiClientProtected() &&
      apiClientProtected()
        .get(pageInfo?.fetchLink)
        .then((res) => {
          if (res.status === 200) {
            switch (PageWithForm) {
              case "schools":
                setPaginationSettings({
                  currentPage: res.data.schools.current_page,
                  totalPages: res.data.schools.last_page,
                  total: res.data.schools.total,
                });
                setData(res.data.schools.data);
                // setCampusesList(res.data.campuses)
                setRelationFields({
                  campus: {
                    options: res.data.campuses,
                    name: "კამპუსი",
                  },
                });
                break;

              case "campuses":
                setPaginationSettings({
                  currentPage: res.data.campuses.current_page,
                  totalPages: res.data.campuses.last_page,
                  total: res.data.campuses.total,
                });
                setData(res.data.campuses.data);
                break;

              case "programs":
                setPaginationSettings({
                  currentPage: res.data.programs.current_page,
                  totalPages: res.data.programs.last_page,
                  total: res.data.programs.total,
                });
                const { school: programs_school, academicDegrees } = res.data;
                setData(res.data.programs.data);

                setRelationFields({
                  school: {
                    options: programs_school,
                    name: "სკოლა",
                  },
                  academic_degree: {
                    options: academicDegrees,
                    name: "აკადემიური ხარისხი",
                  },
                });
                break;

              case "students":
                const {
                  basicOfEnrollments,
                  learnYear,
                  program,
                  school,
                  status,
                  studentGroups,
                  flows,
                  genders,
                  mobility,
                } = res.data;

                setPaginationSettings({
                  currentPage: res.data.students.current_page,
                  totalPages: res.data.students.last_page,
                  total: res.data.students.total,
                  itemsPerPage: res.data.students.per_page,
                });
                setData(res.data.students.data);
                const age = {};
                for (let i = 18; i <= 60; i++) {
                  age[i] = i;
                }

                setRelationFields({
                  basicOfEnrollments: {
                    options: basicOfEnrollments,
                    name: "ჩარიცხვის საფუძვლები",
                  },
                  status: {
                    options: status,
                    name: "სტატუსი",
                  },
                  mobility: {
                    options: mobility,
                    name: "სტატუსი",
                  },
                  sex: {
                    options: { 1: "მამრობითი", 0: "მდედრობითი" },
                    name: "სქესი",
                  },
                  age: {
                    options: age,
                    name: "ასაკი",
                  },
                  school: {
                    options: school,
                    name: "სკოლა",
                  },
                });
                break;

              case "bachelor":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });
                const data = res.data.data.map((item) => {
                  return {
                    ...item.register_form_info,
                    ...item,
                  };
                });
                setData(data);
                //console.log(res, "dgushi");
                setRelationFields({
                  basicOfEnrollments: {
                    options: basicOfEnrollments,
                    name: "ჩარიცხვის საფუძვლები",
                  },
                  learnYear: {
                    options: learnYear,
                    name: "სასწავლო წელი",
                  },
                  program: {
                    options: res.data.programs,
                    name: "პროგრამა",
                  },
                  sex: {
                    options: { 1: "მამრობითი", 0: "მდედრობითი" },
                    name: "სქესი",
                  },
                  status: {
                    options: res.data.additional.status,
                    name: "სტატუსი",
                  },
                });
                break;
              case "master":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });

                const mastersData = res.data.data.map((item) => {
                  return {
                    ...item.register_form_info,
                    ...item,
                  };
                });
                setData(mastersData);
                setRelationFields({
                  basicOfEnrollments: {
                    options: basicOfEnrollments,
                    name: "ჩარიცხვის საფუძვლები",
                  },
                  learnYear: {
                    options: learnYear,
                    name: "სასწავლო წელი",
                  },
                  status: {
                    options: res.data.additional.status,
                    name: "სტატუსი",
                  },
                  sex: {
                    options: { 1: "მამრობითი", 0: "მდედრობითი" },
                    name: "სქესი",
                  },
                });
                break;
              case "phd":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });
                const doctorsData = res.data.data.map((item) => {
                  return {
                    ...item.register_form_info,
                    ...item,
                  };
                });
                setData(doctorsData);
                setRelationFields({
                  basicOfEnrollments: {
                    options: basicOfEnrollments,
                    name: "ჩარიცხვის საფუძვლები",
                  },
                  learnYear: {
                    options: learnYear,
                    name: "სასწავლო წელი",
                  },
                  status: {
                    options: res.data.additional.status,
                    name: "სტატუსი",
                  },
                  sex: {
                    options: { 1: "მამრობითი", 0: "მდედრობითი" },
                    name: "სქესი",
                  },
                });
                break;
              case "tcc":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });
                const trainingsData = res.data.data.map((item) => {
                  return {
                    ...item.register_form_info,
                    ...item,
                  };
                });
                setData(trainingsData);
                setRelationFields({
                  basicOfEnrollments: {
                    options: basicOfEnrollments,
                    name: "ჩარიცხვის საფუძვლები",
                  },
                  learnYear: {
                    options: learnYear,
                    name: "სასწავლო წელი",
                  },
                  status: {
                    options: res.data.additional.status,
                    name: "სტატუსი",
                  },
                  sex: {
                    options: { 1: "მამრობითი", 0: "მდედრობითი" },
                    name: "სქესი",
                  },
                });
                break;
              case "hse":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });
                const professionsData = res.data.data.map((item) => {
                  return {
                    ...item.register_form_info,
                    ...item,
                  };
                });
                setData(professionsData);
                setRelationFields({
                  basicOfEnrollments: {
                    options: basicOfEnrollments,
                    name: "ჩარიცხვის საფუძვლები",
                  },
                  learnYear: {
                    options: learnYear,
                    name: "სასწავლო წელი",
                  },
                  status: {
                    options: res.data.additional.status,
                    name: "სტატუსი",
                  },
                  sex: {
                    options: { 1: "მამრობითი", 0: "მდედრობითი" },
                    name: "სქესი",
                  },
                });
                break;

              case "lecturers":
                setPaginationSettings({
                  currentPage: res.data.lecturers.current_page,
                  totalPages: res.data.lecturers.last_page,
                  total: res.data.lecturers.total,
                  itemsPerPage: res.data.lecturers.per_page,
                });
                //console.log(res);
                setData(res.data.lecturers.data);
                setRelationFields({
                  academic_degree: {
                    options: res.data.academicDegrees,
                    name: "აკადემიური ხარისხი",
                  },
                  direction: {
                    options: res.data.directions,
                    name: "განათლება",
                  },
                  affiliated: {
                    options: { 1: "აკადემიური", 0: "მოწვეული" },
                    name: "აკადემიური/მოწვეული",
                  },
                });
                break;

              case "student-groups":
                //console.log(res);
                setPaginationSettings({
                  currentPage: res.data.studentGroups.current_page,
                  totalPages: res.data.studentGroups.last_page,
                  total: res.data.studentGroups.total,
                  itemsPerPage: res.data.studentGroups.per_page,
                });
                setData(res.data.studentGroups.data);
                setRelationFields({
                  school: {
                    options: res.data.schools,
                    name: "სკოლა",
                  },
                  program: {
                    options: res.data.programs,
                    name: "პროგრამა",
                  },
                });
                break;

              case "administrations":
                setPaginationSettings({
                  currentPage: res.data.administrations.current_page,
                  totalPages: res.data.administrations.last_page,
                  total: res.data.administrations.total,
                  itemsPerPage: res.data.administrations.per_page,
                });
                //console.log(res);
                setData(res.data.administrations.data);
                setRelationFields({
                  positions: {
                    options: res.data.administrationPositions,
                    name: "ადმინისტრაციული პოზიცია",
                  },
                  items: {
                    options: res.data.administrationItems,
                    name: "ადმინისტრაციული ერთეული",
                  },
                  roles: {
                    options: res.data.roles,
                    name: "როლები",
                  },
                  school: {
                    options: res.data.schools,
                    name: "სკოლა",
                  },
                  program: {
                    options: res.data.programs,
                    name: "პროგრამა",
                  },
                });
                break;

              case "administration-positions":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });
                setData(res.data.data);
                break;

              case "administration-items":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });
                setData(res.data.data);
                break;

              case "learn-years":
                setPaginationSettings({
                  currentPage: res.data.learnYears.current_page,
                  totalPages: res.data.learnYears.last_page,
                  total: res.data.learnYears.total,
                  itemsPerPage: res.data.learnYears.per_page,
                });
                setData(res.data.learnYears.data);
                setRelationFields({
                  program: {
                    options: res.data.programs,
                    name: "პროგრამა",
                  },
                });
                break;

              case "flows":
                setPaginationSettings({
                  currentPage: res.data.learnYears.current_page,
                  totalPages: res.data.learnYears.last_page,
                  total: res.data.learnYears.total,
                  itemsPerPage: res.data.learnYears.per_page,
                });
                setData(res.data.learnYears.data);
                // console.log(res.data)
                setRelationFields({
                  school: {
                    options: res.data.schools,
                    name: "სკოლა",
                  },
                  program: {
                    options: res.data.programs,
                    name: "პროგრამა",
                  },
                });
                break;

              case "auditoriums":
                setPaginationSettings({
                  currentPage: res.data.auditoriums.current_page,
                  totalPages: res.data.auditoriums.last_page,
                  total: res.data.auditoriums.total,
                  itemsPerPage: res.data.auditoriums.per_page,
                });
                setData(res.data.auditoriums.data);
                setRelationFields({
                  campuses: {
                    options: res.data.campuses,
                    name: "კამპუსი",
                  },
                });
                break;
              case "roles":
                setPaginationSettings({
                  currentPage: res.data.roles.current_page,
                  totalPages: res.data.roles.last_page,
                  total: res.data.roles.total,
                  itemsPerPage: res.data.roles.per_page,
                });
                setData(res.data.roles);
                setRelationFields({
                  permissions: {
                    options: res.data.permissions,
                    name: "დაშვება",
                  },
                  // program: {
                  //   options: res.data.programs,
                  //   name: "პროგრამები",
                  // },
                });
                break;
              case "permissions":
                //console.log(res.data.permissions);
                setPaginationSettings({
                  currentPage: res.data.permissions.current_page,
                  totalPages: res.data.permissions.last_page,
                  total: res.data.permissions.total,
                  itemsPerPage: res.data.permissions.per_page,
                });
                setData(res.data.permissions.data);
                break;
              case "library-lmb":
                setPaginationSettings({
                  currentPage: res.data["library-lmb"].current_page,
                  totalPages: res.data["library-lmb"].last_page,
                  total: res.data["library-lmb"].total,
                  itemsPerPage: res.data["library-lmb"].per_page,
                });
                setData(res.data["library-lmb"].data);
                setRelationFields({
                  // topics: {
                  //   options: res.data.topics,
                  //   name: "თემატიკა",
                  // },
                  // lecturers: {
                  //   options: res.data.lecturers,
                  //   name: "ლექტორები",
                  // },
                });
                break;
              case "library-subject":
                setPaginationSettings({
                  currentPage: res.data.topics.current_page,
                  totalPages: res.data.topics.last_page,
                  total: res.data.topics.total,
                  itemsPerPage: res.data.topics.per_page,
                });
                setData(res.data.topics.data);
                break;
              case "finance-statement":
                setPaginationSettings({
                  currentPage: res.data["finance-statement"].current_page,
                  totalPages: res.data["finance-statement"].last_page,
                  total: res.data["finance-statement"].total,
                  itemsPerPage: res.data["finance-statement"].per_page,
                });

                setData(
                  getFinanceStatements(res.data["finance-statement"].data)
                );
                break;
              case "calendar":
                setData([...res.data.events, ...res.data.lectures]);
                setIsLoading(false);
                break;
              case "journal":
                setData(res.data.journal.data);
                setPaginationSettings({
                  currentPage: res.data.journal.current_page,
                  totalPages: res.data.journal.last_page,
                  total: res.data.journal.total,
                  itemsPerPage: res.data.journal.per_page,
                });
                setRelationFields({
                  flow_id: {
                    options: res.data.learnYears,
                    name: "სემესტრი",
                  },
                  school: {
                    options: res.data.school,
                    name: "სკოლა",
                  },
                });
                break;
              case "assessments":
                setPaginationSettings({
                  currentPage: res.data.assessments.current_page,
                  totalPages: res.data.assessments.last_page,
                  total: res.data.assessments.total,
                  itemsPerPage: res.data.assessments.per_page,
                });
                setData(res.data.assessments.data);
                setRelationFields({
                  types: {
                    options: {},
                    name: "შეფასებები",
                  },
                });
                break;
              case "administration":
                setPaginationSettings({
                  currentPage: res.data.administrators.current_page,
                  totalPages: res.data.administrators.last_page,
                  total: res.data.administrators.total,
                  itemsPerPage: res.data.administrators.per_page,
                });
                const adminData = res.data.administrators.data.map((item) => {
                  let newObject = "";
                  if (item.hr_administration_info) {
                    newObject = {
                      ...item,
                      ...item.hr_administration_info,
                      ...item.hr_administration_info
                        .hr_administration_educations,
                      ...item.hr_administration_info
                        .hr_administration_appointment,
                    };
                    delete newObject.hr_administration_info;
                    return newObject;
                  } else {
                    return item;
                  }
                });
                setData(adminData);
                setRelationFields({
                  administrationItems: {
                    options: res.data.administrationItems,
                    name: "სამუშაო ადგილი",
                  },
                });
                break;
              case "academic":
                setPaginationSettings({
                  currentPage: res.data.lecturers.current_page,
                  totalPages: res.data.lecturers.last_page,
                  total: res.data.lecturers.total,
                  itemsPerPage: res.data.lecturers.per_page,
                });
                const ldata = res.data.lecturers.data.map((item) => {
                  let newObject = "";
                  if (item.hr_academic_lecture_info) {
                    newObject = {
                      ...item.hr_academic_lecture_info,
                      ...item.hr_academic_lecture_info
                        .hr_academic_lecture_position,
                      ...item.hr_academic_lecture_info
                        .hr_academic_lecture_additional,
                      ...item,
                    };
                    delete newObject.hr_academic_lecture_info;
                    return newObject;
                  } else {
                    return item;
                  }
                });
                setData(ldata);
                setRelationFields({
                  academic_degree: {
                    name: "აკადემიური ხარისხი",
                    options: res.data.academicDegrees,
                  },
                });
                break;
              case "invited":
                setPaginationSettings({
                  currentPage: res.data.lecturers.current_page,
                  totalPages: res.data.lecturers.last_page,
                  total: res.data.lecturers.total,
                  itemsPerPage: res.data.lecturers.per_page,
                });
                let newObject = "";
                const lecturersData = res.data.lecturers.data.map((item) => {
                  if (item.hr_invited_lecture_info) {
                    newObject = {
                      ...item.hr_invited_lecture_info,
                      ...item.hr_invited_lecture_info
                        .hr_invited_lecture_position,
                      ...item.hr_invited_lecture_info
                        .hr_invited_lecture_additional,
                      ...item,
                    };
                    delete newObject.hr_invited_lecture_info;
                    return newObject;
                  } else {
                    return item;
                  }
                });
                setData(lecturersData);
                setRelationFields({
                  workTypes: {
                    options: res.data.workTypes,
                    name: "სამუშაო ადგილი",
                  },
                  academic_degree: {
                    name: "აკადემიური ხარისხი",
                    options: res.data.academicDegrees,
                  },
                });
                break;
              case "news":
                setPaginationSettings({
                  currentPage: res.data.news.current_page,
                  totalPages: res.data.news.last_page,
                  total: res.data.news.total,
                  itemsPerPage: res.data.news.per_page,
                });
                setData(res.data.news.data);
                break;
              case "grade-analyze":
                setData(res.data.news.data);
                break;
              case "edoc":
                setPaginationSettings({
                  currentPage: res.data.templates.current_page,
                  totalPages: res.data.templates.last_page,
                  itemsPerPage: res.data.templates.per_page,
                });
                //console.log(res.data);
                for (let i = 0; i < res.data.templates.length; i++) {
                  for (let j = 0; j < res.data.userTypes.length; j++) {
                    if (
                      res.data.templates[i].user_type_id ===
                      res.data.userTypes[j].id
                    ) {
                      res.data.templates[i].status =
                        res.data.userTypes[j].title;
                    }
                  }
                }
                setData(res.data.templates.data);
                setRelationFields({
                  // types: {
                  //   options: Object.entries(res.data.userTypes).map((item) => ({
                  //     id: item[1],
                  //     name: item[0],
                  //   })),
                  //   name: "კამპუსი",
                  // },
                });
                break;
              case "edoc-inbox":
                setPaginationSettings({
                  currentPage: res.data.edocs.current_page,
                  totalPages: res.data.edocs.last_page,
                  total: res.data.edocs.total,
                  itemsPerPage: res.data.edocs.per_page,
                });
                setData(res.data.edocs.data);
                break;
              case "edoc-sent":
                setPaginationSettings({
                  currentPage: res.data.edocs.current_page,
                  totalPages: res.data.edocs.last_page,
                  total: res.data.edocs.total,
                  itemsPerPage: res.data.edocs.per_page,
                });
                setData(res.data.edocs.data);
                break;
              case "surveys":
                setPaginationSettings({
                  currentPage: res.data.surveys.current_page,
                  totalPages: res.data.surveys.last_page,
                  total: res.data.surveys.total,
                  itemsPerPage: res.data.surveys.per_page,
                });
                setData(res.data.surveys.data);
                setRelationFields({
                  types: {
                    options: res.data.questionTypes,
                    name: "კითხვის ტიპები",
                  },
                });
                break;
              case "minor-logs":
                setPaginationSettings({
                  currentPage: res.data.current_page,
                  totalPages: res.data.last_page,
                  total: res.data.total,
                  itemsPerPage: res.data.per_page,
                });
                setData(res.data.data);

                // Fetch flows data from NEXT_PUBLIC_LEARN_YEARS endpoint
                apiClientProtected().get(process.env.NEXT_PUBLIC_LEARN_YEARS)
                  .then(flowsResponse => {
                    const flowsOptions = {};
                    if (flowsResponse.data.learnYears && flowsResponse.data.learnYears.data) {
                      flowsResponse.data.learnYears.data.forEach(flow => {
                        flowsOptions[flow.id] = flow.name;
                      });
                    }

                    setRelationFields({
                      flows: {
                        options: flowsOptions,
                        name: "სასწავლო სემესტრი",
                      },
                    });
                  })
                  .catch(error => {
                    console.error("Error fetching flows data:", error);
                    // Fallback to original flows data if available
                    setRelationFields({
                      flows: {
                        options: res.data.flows || {},
                        name: "სასწავლო სემესტრი",
                      },
                    });
                  });
                break;
              default:
                break;
            }
          }
        })
        .catch((err) => {
          //console.log(err);
          if (pageInfo && pageInfo.routeName === "journal") {
            setData([
              {
                id: 1,
                code: "1232as",
                subject: "საერთაშორისო სამართალი",
                lecturers: "Teresa Robbins",
                group_number: "3",
                course: "2",
              },
            ]);
          }
        });

    return () => {
      setCampusesList(null);
    };
  }, [pageInfo]);

  return (
    <Context.Provider
      value={{
        data,
        setData,
        pageInfo,
        setPageInfo,
        handleDataDeletion,
        handleDataEdit,
        handleDataSubmit,
        handleFilters,
        PageWithForm,
        openModal,
        setOpenModal,
        modalType,
        isLoading,
        setModalType,
        campusesList,
        errors,
        filterBadges,
        handleFilterDelete,
        setErrors,
        tableData,
        setTableData,
        relationFields,
        setRelationFields,
        shrinkSidebar,
        setShrinkSidebar,
        shrinkSidebarOnHover,
        setShrinkSidebarOnHover,
        booksRow,
        setBooksRow,
        flowId,
        setFlowId,
        programId,
        setProgramId,
        surveyId,
        setSurveyId,
        questionId,
        groupId,
        setGroupId,
        setQuestionId,
        statusId,
        setStatusId,
        alertMessage,
        setAlertMessage,
        periods,
        academicDegreeId,
        setAcademicDegreeId,
        search,
        setSearch,
        paginationSettings,
        setPaginationSettings,
        signsDataUpdate,
        setSignsDataUpdate,
        dataId,
        setDataId,
        handleSyllabusEdit,
        exportParams,
        setExportParams,
      }}
    >
      {children}
    </Context.Provider>
  );
}

export function useTableContext() {
  return useContext(Context);
}
