import { useState, useEffect } from "react";
import styled from "styled-components";
import About from "./About";
import Contact from "./Contact";
import Edu from "./Edu";
import HowDidUhear from "./HowDidUhear";
import Personal from "./Personal";
import Address from "./Address";
import Privacy from "./Privacy";
import Recommendations from "./Recommendations";
import Uploads from "./Uploads";
import ButtonLoader from "../ui/ButtonLoader";
import { sources, PROGRAM_NAMES, PROGRAM_LEVEL } from "./registerData";
import { useLocaleContext } from "../context/LocaleContext";
import { apiClient } from "../../helpers/apiClient";
import SweetAlert2 from "react-sweetalert2";
import { langs } from "../locale";
import WorkingExperience from "./WorkingExperience";
import { useTableContext } from "../context/TableContext";
import { useRouter } from "next/router";
import { clearDefaultStyles, container, label } from "./styled-css";

const RegisterTcc = () => {
  const { setErrors } = useTableContext();
  const router = useRouter();
  const { locale } = useLocaleContext();
  const [isAgreed, setIsAgreed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorText, setErrorText] = useState("");
  const [heardAbouts, setHeardAbouts] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [academicDegrees, setAcademicDegrees] = useState([]);
  const [success, setSuccess] = useState(false);
  const [swalProps, setSwalProps] = useState({});
  const [user, setUser] = useState({
    firstName: "",
    lastName: "",
    firstNameEn: "",
    lastNameEn: "",
    cardNumber: "",
    birthDay: "",
    birthMonth: "",
    birthYear: "",
    phone: "",
    program_id: "",
    email: "",
    address: "",
    city: "",
    city_of_birth: "",
    street: "",
    comment: "",
    hearAbout: [],
    employment_status: "0",
    experience: [
      {
        id: 1,
        company: "",
        position: "",
        start_date: "",
        end_date: "",
        field: "",
      },
    ],
    educations: [
      {
        id: 1,
        university: "",
        faculty: "",
        academic_degree_id: "",
        start_date: "",
        end_date: "",
      },
    ],
    programs: [{ id: 1, program_id: "", level_id: "" }],
    langs: [{ id: 1, language: "", certificate: "" }],
    recommendations: [{ id: 1, person: "", phone: "" }],
    english_level_id: "",
    schoolDocument: "",
    examDocument: "",
    identityNumber: "",
    idNumber: "",
    motivationLetter: "",
    doc: "",
    certificates: "",
    usefulthing: "",
  });

  const [filenames, setFilenames] = useState({
    schoolDocument: "",
    examDocument: "",
    identityNumber: "",
    motivationLetter: "",
    doc: "",
  });

  useEffect(() => {
    // setErrors(null)
    const getPrograms = async () => {
      const response = await apiClient().get("/academic-degrees");
      setAcademicDegrees(response.data);
      const programsResponse = await apiClient().get(
        "/programs-by-academic-degree/5?registration_form=1"
      );
      setPrograms(programsResponse.data);
      setHeardAbouts(sources);
    };

    getPrograms();
  }, []);

  const handleChange = (e, value) => {
    if (e.target.type === "file" && e.target.name === "certificates") {
      setUser({ ...user, [e.target.name]: e.target.files });
      setFilenames({ ...filenames, [e.target.name]: e.target.files[0].name });
    } else if (e.target.type === "file") {
      setUser({ ...user, [e.target.name]: e.target.files[0] });
      setFilenames({ ...filenames, [e.target.name]: e.target.files[0].name });
    } else {
      setUser({ ...user, [e.target.name]: e.target.value });
    }
  };

  const handleGender = (id) => {
    setUser({ ...user, gender: id });
  };

  const handleFullname = (e, index) => {
    const aboutsArray = user.hearAbout.map((item) => {
      if (item.id === index) {
        item.fullName = e.target.value;
      }
      return item;
    });
    setUser({ ...user, hearAbout: aboutsArray });
  };

  const addItem = (data) => {
    const values = [...user[data]];
    if (data === "educations") {
      values.push({
        id: Math.floor(Math.random() * 100),
        school: "",
        field: "",
        date: "",
      });
    } else if (data === "programs") {
      values.push({
        id: Math.floor(Math.random() * 100),
        program_id: "",
        level_id: "",
      });
    } else {
      values.push({
        id: Math.floor(Math.random() * 100),
        language: "",
        certificate: "",
      });
    }
    setUser({
      ...user,
      [data]: values,
    });
    // console.log('Add School')
  };

  const deleteEducation = (name, id) => {
    const data = [...user[name]].filter((item) => item.id !== id);
    //console.log(user[name], name, id);
    setUser({ ...user, [name]: data });
  };

  const handleData = (event, index, field) => {
    const values = [...user[field]];
    const updatedValue = event.target.name;
    values[index][updatedValue] = event.target.value;
    setUser({
      ...user,
      [field]: values,
    });
  };

  const handleSelect = (name, value, key, index) => {
    //console.log(name, value, key, index);
    if (key) {
      const values = [...user[key]];
      const updatedValue = name;
      //console.log(values, updatedValue, values[index]);
      values[index][updatedValue] = value;
      setUser({
        ...user,
        [key]: values,
      });
    } else {
      setUser({ ...user, [name]: value });
    }
  };

  const addMethod = (e) => {
    // console.log(e.target.checked)
    const checkedMethod = JSON.parse(e.target.value);
    if (e.target.checked) {
      // console.log(e.target.value)
      const checkedData = sources.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = true;
        }
        return item;
      });
      setHeardAbouts(checkedData);
      setUser({
        ...user,
        hearAbout: [...user.hearAbout, checkedMethod],
      });
    } else {
      const filteredMethods = user.hearAbout.filter(
        (item) => item.id !== checkedMethod.id
      );
      setUser({
        ...user,
        hearAbout: filteredMethods,
      });
      const checkedData = heardAbouts.map((item) => {
        if (item.id === checkedMethod.id) {
          item.isAdded = false;
        }
        return item;
      });
      setHeardAbouts(checkedData);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const birthDate =
      user.birthDay && user.birthMonth && user.birthYear
        ? user.birthDay + "-" + user.birthMonth + "-" + user.birthYear
        : "";

    const fd = new FormData();
    fd.append("first_name", user.firstName);
    fd.append("last_name", user.lastName);
    fd.append("first_name_en", user.firstNameEn);
    fd.append("last_name_en", user.lastNameEn);
    fd.append("identity_number", user.idNumber || "");
    fd.append("gender", user.gender);
    fd.append("card_number", user.cardNumber);
    fd.append("phone", user.phone);
    fd.append("date_of_birth", birthDate);
    fd.append("program_id", user.program_id);
    // fd.append("program_id", "1");
    fd.append("email", user.email);
    fd.append("city", user.city);
    fd.append("city_of_birth", user.city_of_birth);
    fd.append("street", user.street);
    fd.append("english_level_id", user.english_level_id);
    fd.append("school", user.school);
    fd.append("employment_status", user.employment_status);
    fd.append("about_university", "asdasdkjh");

    fd.append("diploma_copy", user.schoolDocument);
    fd.append("identity_number_copy", user.identityNumber);
    fd.append("motivation_letter", user.motivationLetter);
    fd.append("marks_paper", user.examDocument);
    fd.append("exam_document", user.cv);
    fd.append("finished_university_info", user.examDocument);
    fd.append("usefulthing", user.usefulthing);

    for (let i = 0; i < user.educations.length; i++) {
      for (let key in user.educations[i]) {
        fd.append(`educations[${i}][${key}]`, user.educations[i][key]);
      }
    }

    for (let i = 0; i < user.programs.length; i++) {
      for (let key in user.programs[i]) {
        fd.append(`programs[${i}][${key}]`, user.programs[i][key]);
      }
    }

    for (let i = 0; i < user.recommendations.length; i++) {
      for (let key in user.recommendations[i]) {
        fd.append(
          `recommendations[${i}][${key}]`,
          user.recommendations[i][key]
        );
      }
    }

    for (let i = 0; i < user.certificates.length; i++) {
      // console.log((item), 'file_name');
      fd.append(`certificates[${i}]`, user.certificates[i]);
    }

    //console.log(user);

    try {
      const response = await apiClient().post("/training-registers/store", fd);
      setSuccess(true);
      setSwalProps({
        show: true,
        title: "დიდი მადლობა!",
        text: "თქვენ წარმატებით გაიარეთ რეგისტრაცია! უახლოეს მომავალში დაგიკავშირდებით <3",
        icon: "success",
        confirmButtonColor: "#009ef7",
        didClose: () => console.log("gipa"),
      });
      setIsLoading(false);
      setErrorText("");
      router.push("/login");
    } catch (err) {
      setIsLoading(false);
      setErrorText("გთხოვთ, შეავსოთ შესაბამისი ველები *");
      // console.log(err.response.data);
      setErrors(err.response.data.errors);
    }
  };

  return (
    <>
      <Wrapper>
        <form onSubmit={handleSubmit}>
          <About handleSelect={handleSelect} programs={programs} />

          <Personal
            handleChange={handleChange}
            handleGender={handleGender}
            gender={user.gender}
            birthYearRange={18}
            handleSelect={handleSelect}
            showIdNumber={false}
            type="4"
            formType="Training"
          />

          <Contact handleChange={handleChange} type="4" formType="Training" />

          <Address handleChange={handleChange} />

          <Edu
            addItem={addItem}
            handleData={handleData}
            educationData={user.educations}
            experienceData={user.experience}
            programsData={user.programs}
            academicDegrees={academicDegrees}
            handleSelect={handleSelect}
            handleChange={handleChange}
            programNames={PROGRAM_NAMES}
            programLevel={PROGRAM_LEVEL}
            isCertificate={false}
            deleteEducation={deleteEducation}
            type="4"
            formType="Training"
          />

          <WorkingExperience
            addItem={addItem}
            handleData={handleData}
            handleChange={handleChange}
            experienceData={user.experience}
            type="4"
            birthYearRange={20}
            handleSelect={handleSelect}
            deleteEducation={deleteEducation}
          />

          {/* <Recommendations
            addItem={addItem}
            handleData={handleData}
            type="4"
            data={user.recommendations}
          /> */}
          <CommentWrapper>
            <label htmlFor="">დაამატეთ კომენტარი:</label>
            <TextComment
              name="comment"
              value={user.comment}
              onChange={handleChange}
            />
          </CommentWrapper>
          <Uploads handleChange={handleChange} filenames={filenames} type="4" />
          <HowDidUhear
            addMethod={addMethod}
            handleFullname={handleFullname}
            sources={heardAbouts}
            title="საიდან გაიგეთ ჩვენი ტრეინინგის პროგრამების შესახებ?"
          />
          <Privacy setIsAgreed={setIsAgreed} />
          <input type="hidden" name="usefulthing" onChange={handleChange} />

          <ButtonController>
            <Button type="submit" disabled={!isAgreed || isLoading}>
              {isLoading ? (
                <ButtonLoader />
              ) : (
                locale && langs[locale]["register"]
              )}
            </Button>
            {errorText && (
              <div className="text-danger error-message text-center mt-4">
                {errorText}
              </div>
            )}
          </ButtonController>
        </form>
      </Wrapper>
      {success && (
        <SweetAlert2 {...swalProps} onConfirm={() => setSuccess(false)} />
      )}
    </>
  );
};

const Wrapper = styled.div`
  ${container}
  label {
    ${label}
  }

  form {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 50px 0;
  }
`;

const CommentWrapper = styled.div`
  width: 100%;
`;

const TextComment = styled.textarea`
  width: 100%;
  background: #f7f7f7;
  border: 1px solid #eee;
  border-radius: 10px;
  height: 100px;
  transition: all 300ms;
  padding: 20px;
  outline: none;
  &:hover {
    background: #efefef;
  }
`;

const Button = styled.button`
  ${clearDefaultStyles}
  height: 46px;
  width: 200px;
  background-color: #307ae9;
  border-radius: 10px;
  font-family: "FiraGO", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 26px;
  color: #ffffff;
  transition: all 0.5s ease;
  :hover {
    background-color: #72a9fa;
    cursor: pointer;
  }
  :disabled {
    background: #72a9fa;
    cursor: default;
  }
`;

const ButtonController = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export default RegisterTcc;
