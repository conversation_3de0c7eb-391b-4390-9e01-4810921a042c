import { useRef } from "react";
import styled from "styled-components";
import { upload, uploaddark } from "./svg";
import Title from "./Title";
import Paragraph from "./Paragraph";
import Action from "./Action";
import { useTableContext } from "../context/TableContext";
import {
  fieldArea,
  fourCol,
  grid,
  oneCol,
  threeCol,
  twoCol,
} from "./styled-css";

const Uploads = ({
  handleChange,
  filenames,
  type,
  militaryIsNeeded,
  formType,
}) => {
  const examRef = useRef(null);
  const idRef = useRef(null);
  const cvRef = useRef(null);
  const letterRef = useRef(null);
  const docRef = useRef(null);
  const imageRef = useRef(null);
  const schoolRef = useRef(null);
  const paymentRef = useRef(null);
  const militaryRef = useRef(null);
  const diplomaRef = useRef(null);

  const triggerUpload = (name) => {
    name.current.click();
  };

  const { errors } = useTableContext();

  return (
    <Wrapper>
      <Title image={upload} text="გთხოვთ, ატვირთოთ დოკუმენტაცია:" />
      <Paragraph text="*დაშვებული ფორმატი: pdf,jpg,png,doc,docx" />
      <Fields className={formType === "Bachelory" ? "four-col" : ""}>
        <Item onClick={() => triggerUpload(idRef)}>
          <input
            type="file"
            accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
            className="d-none"
            name="identityNumber"
            onChange={handleChange}
            ref={idRef}
          />
          <label>პირადობის ასლი:</label>
          <UploadField>{uploaddark}</UploadField>
          <div>{filenames.identityNumber}</div>
          {errors && (
            <div className="text-danger error-message">
              {errors.identity_number_copy}
            </div>
          )}
        </Item>

        {type === "2" && (
          <Item onClick={() => triggerUpload(diplomaRef)}>
            <input
              type="file"
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
              className="d-none"
              name="diplomaCopy"
              onChange={handleChange}
              ref={diplomaRef}
            />
            <label>
              დიპლომი/ცნობა უნივერსიტეტიდან{" "}
              {/* <span style={{ fontSize: "10px" }}>(არასავალდებულო ველი)</span>: */}
            </label>
            <UploadField>{uploaddark}</UploadField>
            <div>{filenames.diplomaCopy}</div>
            {errors && (
              <div className="text-danger error-message">
                {errors.diploma_copy}
              </div>
            )}
          </Item>
        )}

        {type === "2" && (
          <Item onClick={() => triggerUpload(examRef)}>
            <input
              type="file"
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
              className="d-none"
              name="examDocument"
              onChange={handleChange}
              ref={examRef}
            />
            <label>ნიშნების ფურცელი:</label>
            <UploadField>{uploaddark}</UploadField>
            <div>{filenames.examDocument}</div>
            {errors && (
              <div className="text-danger error-message">
                {errors.marks_paper}
              </div>
            )}
          </Item>
        )}
        {(type === "2" || type === "3" || type === "5") && (
          <Item onClick={() => triggerUpload(cvRef)}>
            <input
              type="file"
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
              className="d-none"
              name="cv"
              onChange={handleChange}
              ref={cvRef}
            />
            <label>CV:</label>
            <UploadField>{uploaddark}</UploadField>
            <div>{filenames.cv}</div>
            {errors && (
              <div className="text-danger error-message">{errors.cv}</div>
            )}
          </Item>
        )}
        {type === "2" && (
          <Item onClick={() => triggerUpload(letterRef)}>
            <input
              type="file"
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
              className="d-none"
              name="motivationLetter"
              onChange={handleChange}
              ref={letterRef}
            />
            <label>საერთო სამაგისტრო გამოცდებში მონაწილეობის დამადასტურებელი ბარათი:</label>
            <UploadField>{uploaddark}</UploadField>
            <div>{filenames.motivationLetter}</div>
            {errors && (
              <div className="text-danger error-message">
                {errors.motivation_letter}
              </div>
            )}
          </Item>
        )}
        {/*{type === "2" && (*/}
        {/*  <Item onClick={() => triggerUpload(docRef)}>*/}
        {/*    <input*/}
        {/*      type="file"*/}
        {/*      accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"*/}
        {/*      className="d-none"*/}
        {/*      name="doc"*/}
        {/*      onChange={handleChange}*/}
        {/*      ref={docRef}*/}
        {/*    />*/}
        {/*    <label>გამოცდებში მონაწილეობის დამადასტურებელი ბარათი:</label>*/}
        {/*    <UploadField>{uploaddark}</UploadField>*/}
        {/*    <div>{filenames.doc}</div>*/}
        {/*    {errors && (*/}
        {/*      <div className="text-danger error-message">*/}
        {/*        {errors.exam_document}*/}
        {/*      </div>*/}
        {/*    )}*/}
        {/*  </Item>*/}
        {/*)}*/}

        {(type === "1" || type === "3" || type === "5") && (
          <Item onClick={() => triggerUpload(imageRef)}>
            <input
              type="file"
              accept="image/png, image/jpeg"
              className="d-none"
              name="image"
              onChange={handleChange}
              ref={imageRef}
            />
            <label>ფოტო:</label>
            <UploadField>{uploaddark}</UploadField>
            {filenames.image && <div>{filenames.image}</div>}
            {errors && (
              <div className="text-danger error-message">{errors.photo}</div>
            )}
          </Item>
        )}
        {type === "1" && (
          <Item onClick={() => triggerUpload(schoolRef)}>
            <input
              type="file"
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
              className="d-none"
              name="schoolDocument"
              onChange={handleChange}
              ref={schoolRef}
            />
            <label>ატესტატი:</label>
            <UploadField>{uploaddark}</UploadField>
            <div>{filenames.schoolDocument}</div>
            {errors && (
              <div className="text-danger error-message">
                {errors.school_document}
              </div>
            )}
          </Item>
        )}
        {type === "1" && (
          <Item onClick={() => triggerUpload(paymentRef)}>
            <input
              type="file"
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
              className="d-none"
              name="paymentCopy"
              onChange={handleChange}
              ref={paymentRef}
            />
            <label>
              სწავლის წლიური საფასურის 25% გადახდის დამადასტურებელი ქვითარი:
            </label>
            <UploadField>{uploaddark}</UploadField>
            <div>{filenames.paymentCopy}</div>
            {errors && (
              <div className="text-danger error-message">
                {errors.payment_document}
              </div>
            )}
          </Item>
        )}
        {type === "1" && militaryIsNeeded === "1" && (
          <Item onClick={() => triggerUpload(militaryRef)}>
            <input
              type="file"
              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword, image/png, image/jpeg"
              className="d-none"
              name="militaryAccounting"
              onChange={handleChange}
              ref={militaryRef}
            />
            <label>სამხედრო საბუთი:</label>
            <UploadField>{uploaddark}</UploadField>
            <div>{filenames.militaryAccounting}</div>
            {errors && (
              <div className="text-danger error-message">
                {errors.military_accounting}
              </div>
            )}
          </Item>
        )}
      </Fields>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  p {
    color: #073882;
    font-style: italic;
    max-width: 600px;
  }
  .four-col {
    ${fourCol}
    @media (max-width: 992px) {
      ${oneCol}
    }
  }
  .error-message {
    position: absolute;
    left: 4px;
    top: 100%;
  }
`;

const Fields = styled.div`
  ${grid}
  ${threeCol}
  align-items: end;
  @media (max-width: 992px) {
    ${oneCol}
  }
`;

const Item = styled.div`
  position: relative;
`;

const UploadField = styled.div`
  ${fieldArea}
  padding: 11px 20px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  svg {
    width: 25px;
  }
`;

export default Uploads;
