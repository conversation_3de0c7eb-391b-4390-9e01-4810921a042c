import styled from "styled-components";
import { useState, useEffect, useRef } from "react";
import useOutsideClick from "./../custom_hooks/useOutsideClick";
import { fieldArea, placeholder } from "./styled-css";

const Select = ({
  selected,
  handleSelect,
  name,
  propertyKey,
  itemIndex,
  data,
  text,
  image,
}) => {
  const [isActive, setIsActive] = useState(false);
  const [label, setLabel] = useState("");

  const elementRef = useRef(null);

  useEffect(() => {
    setLabel(selected);
    // console.log(label, selected)
  }, []);

  useOutsideClick(elementRef, () => setIsActive(false));

  const selectHandler = () => {
    setIsActive(!isActive);
  };

  const setValue = (option, index) => {
    const value = typeof option === "object" ? option.id : option;
    handleSelect(name, value, propertyKey, itemIndex);
    // console.log(propertyKey, "Select");
    setLabel(
      typeof option === "object" ? option.name || option.name_ka : option
    );
    setIsActive(false);
  };

  return (
    <Item ref={elementRef}>
      <DropdownBtn onClick={selectHandler} isActive={isActive}>
        {label}
        {image}
      </DropdownBtn>
      {isActive ? (
        <OptionsContainer>
          {data?.map((option, index) => (
            <Option
              key={index}
              //   value={option}
              onClick={() => setValue(option, index)}
            >
              {typeof option === "object"
                ? option.name_ka || option.name
                : option}
              {/* {option} */}
            </Option>
          ))}
        </OptionsContainer>
      ) : (
        ""
      )}
    </Item>
  );
};

const Item = styled.div`
  width: 100%;
  position: relative;
  padding: 7px;
  ${fieldArea}
`;

const DropdownBtn = styled.div`
  ${placeholder}
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0 10px;
  svg {
    transition: 0.5s ease;
    transform: ${(props) =>
      props.isActive ? "rotate(180deg)" : "rotate(0deg)"};
  }
`;

const OptionsContainer = styled.div`
  background-color: #f7f7f7;
  border-radius: 10px;
  padding: 0px;
  width: 100%;
  top: 50px;
  left: -2px;
  position: absolute;
  z-index: 15;
  max-height: 200px;
  overflow: hidden;
  overflow-y: auto;

  /* Custom scrollbar styling */

  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #989898 #f1f1f1;

  box-shadow: 0px 16px 24px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04),
  0px 0px 1px rgba(0, 0, 0, 0.04);
`;

const Option = styled.div`
  border-bottom: solid 1px #ffff;
  padding: 10px;
  padding-bottom: 20px;
  transition: all 0.5s ease;
  overflow: hidden;
  :hover {
    background-color: #efefef;
    cursor: pointer;
  }
  :last-child {
    border-bottom: none;
  }
`;

export default Select;
