export const langs = {
  ka: {
    admin: "მთავარი",
    profile: "პროფილი",
    edit: "რედაქტირება",
    view: "ნახვა",
    delete: "წაშლა",
    create: "შექმნა",
    action: "მოქმედება",
    close: "დახურვა",
    export: "ექსპორტი",
    import: "იმპორტი",
    copy: "კოპირება",
    filters: "ფილტრი",
    add: "დამატება",
    male: "მამრობითი",
    female: "მდედრობითი",
    library: "ბიბლიოთეკა",
    add_book: "წიგნის დამატება",
    profile_edit: "პროფილის განახლება",
    photo: "ფოტო",
    full_name: "სრული სახელი",
    name_ka: "სახელი ქართულად",
    name_en: "სახელი ინგლისურად",
    surname_en: "გვარი ინგლისურად",
    first_name: "სახელი",
    last_name: "გვარი",
    id_number: "პირადი ნომერი",
    id_card_number: "პირადობის ბარათის ნომერი",
    citizenship: "მოქალაქეობა",
    mobility: "მობილურობა",
    groups: "ჯგუფები",
    group: "ჯგუფი",
    phone: "ტელეფონი",
    email: "ელ-ფოსტა",
    admin_position: "ადმინისტრაციული პოზიცია",
    admin_school: "ადმინისტრაციული სკოლა",
    title: "სათაური",
    author: "ავტორი",
    lecturer: "ლექტორი",
    topic: "თემატიკა",
    release_date: "გამოშვების თარიღი",
    subject: "საგანი",
    book_upload: "წიგნის ატვირთვა",
    logout: "გამოსვლა",
    login: "შესვლა",
    forgot_pass: "დაგავიწყდა პაროლი",
    recovery: "აღდგენა",
    password_recovery: "პაროლის აღდგენა",
    password: "პაროლი",
    enter_password: "შეიყვანეთ პაროლი",
    enter_new_password: "შეიყვანეთ ახალი პაროლი",
    enter_email: "შეიყვანეთ ელ-ფოსტა",
    with_google: "ელ-ფოსტით ავტორიზაცია",
    language: "ენა",
    syllabus: "სილაბუსი",
    lecture_hours: "სალექციო საათები",
    creating_sylabus: "სილაბუსის შექმნა",
    name_of_tr_course: "სასწავლო კურსის დასახელება",
    name_of_course: "კურსის დასახელება",
    course_level: "სასწავლო კურსის საფეხური",
    bachelor: "ბაკალავრიატი",
    master: "მაგისტრატურა",
    phd: "დოქტორანტურა",
    tcc: "ტრენინგ ცენტრი",
    hse: "HSE",
    english_level_id: "ინგლისური ენის დონე",
    course_status: "სასწავლო კურსის სტატუსი",
    semester_of_course_impl: "სასწავლო კურსის განხორციელების სემესტრი",
    course_lecturer:
      "კურსის განმახროციელებელი/ ლექტორი საკონტაქტო ინფორმაცია სტუდენტებთან საკონსულტაციო შეხვედრისთვის გამოყოფილი დღე და საათი",
    semester: "სემესტრი",
    ects_credits: "ECTS კრედიტების რაოდენობა",
    credit: "კრედიტი",
    point_marker: "შეფასება",
    dist_of_hours: "საათების გადანაწილება სტუდენტის დატვირთვის შესაბამისად",
    goals_of_course: "სასწავლო კურსის მიზნები",
    learning_process: "სწავლება-სწავლის მეთოდები",
    assess_components: "შეფასების კომპონენტები",
    eval_system: "შეფასების სისტემა",
    admission_preconditions: "საგანზე დაშვების წინაპირობა/ები",
    learning_outcomes: "სწავლის შედეგები",
    score: "შეფასება",
    min_score: "მინ. ზღვარი",
    course_code: "სასწავლო კურსის კოდი",
    code: "კოდი",
    skill: "უნარი",
    assessment: "შეფასება",
    num_of_credits: "კრედიტების რაოდენობა",
    quantity: "რაოდენობა",
    contact_hours: "საკონტაქტო საათები",
    free_hours: "დამოუკიდებელი საათები",
    lecture: "ლექცია",
    seminar: "სემინარი",
    topics_and_activities:
      "სასწავლო კურსის/მოდულის შინაარსი (თემები და აქტივობები)",
    recovery_missed_component: "გაცდენილი შეფასებითი კომპონენტის აღდგენა",
    academic_plagiarism: "აკადემიური კეთილსინიდსიერების დაღვევა - პლაგიატი",
    prerequisites: "წინაპირობები",
    prerequisite_for_admission:
      "დასკვნით გამოცდაზე დაშვების წინაპირობა და დამატებით გამოცდაზე გასვლის პირობა",
    Int_and_final_eval: "შუალედური და დასკვნითი შეფასებები",
    total: "ჯამი",
    logo_title: "საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტი",
    students: "სტუდენტები",
    lecturers: "ლექტორები",
    roles: "როლები",
    management: "მართვა",
    permission: "ნებართვა",
    administration: "ადმინისტრაცია",
    administrative: "ადმინისტრაციული",
    admin_positions: "ადმინისტრაციული პოზიციები",
    admin_units: "ადმინისტრაციული ერთეული",
    learning_plan: "სასწავლო გეგმა",
    student_groups: "სტუდენტური ჯგუფები",
    flows: "ნაკადები",
    academic_years: "სასწავლო წლები",
    recommendations: "რეკომენდაცია",
    schools: "სკოლები",
    school: "სკოლა",
    learning_programs: "სასწავლო პროგრამები",
    learning_auditors: "სასწავლო აუდიტორიები",
    auditorium: "აუდიტორია",
    users: "მომხმარებლები",
    campuses: "კამპუსები",
    "library-lmb": "ელ-ბიბლიოთეკა",
    search: "ძებნა",
    add_book: "წიგნის დამატება",
    topic_management: "თემატიკის მართვა",
    curriculum: "კურიკულუმი",
    eval_components: "შეფასების კომპონენტები",
    finances: "ფინანსები",
    concentrations: "კონცენტრაციები",
    ac_performance: "აკადემიური მოსწრება",
    academic_degree: "აკადემიური ხარისხი",
    affiliated: "აკადემიური/მოწვეული",
    calendar: "კალენდარი",
    reg_form: "სარეგისტრაციო ფორმა",
    address: "მისამართი",
    quality_service: "ხარისხის სამსახური",
    table: "ცხრილი",
    silabus_title: "სილაბუსის შექმნა",
    analysis_of_signs: "ნიშნების ანალიზი",
    direct_message: "პერსონალური მიმოწერა",
    journal: "ჟურნალი/უწყისები",
    day: "დღე",
    sunday: "კვირა",
    monday: "ორშაბათი",
    tuesday: "სამშაბათი",
    wednesday: "ოთხშაბათი",
    thursday: "ხუთშაბათი",
    friday: "პარასკევი",
    saturday: "შაბათი",
    sun: "კვ",
    mon: "ორშ",
    tu: "სამ",
    wed: "ოთხ",
    thu: "ხუთ",
    fri: "პარ",
    sat: "შაბ",
    january: "იანვარი",
    february: "თებერვალი",
    march: "მარტი",
    april: "აპრილი",
    may: "მაისი",
    june: "ივნისი",
    july: "ივლისი",
    august: "აგვისტო",
    september: "სექტემბერი",
    october: "ოქტომბერი",
    november: "ნოემბერი",
    december: "დეკემბერი",
    main: "მთავარი გვერდი",
    table: "ცხრილი",
    lists: "სიები",
    messages: "შეტყობინებები",
    payment: "გადახდა",
    req_sent: "მოთხოვნა გაიგზავნა",
    statement: "განცხადებები",
    news: "სიახლეები",
    about_gipa: "GIPA-ს შესახებ",
    radio_gipa: "რადიო GIPA",
    trainings: "ტრენინგები",
    my_subjects: "ჩემი საგნები",
    academic_year: "2024-2025 სასწავლო წელი",
    past_subjects: "განვლილი საგნები",
    career: "კარიერა",
    projects: "პროექტები",
    vacancies: "ვაკანსიები",
    jstor_base: "EBSCO & JSTOR ბაზები",
    proxy_config: "Proxy კონფიგურაცია",
    book_search: "წიგნის ძებნა",
    change_password: "პაროლის შეცვლა",
    current_password: "მიმდინარე პაროლი",
    new_password: "ახალი პაროლი",
    confirm_new_password: "გაიმეორეთ ახალი პაროლი",
    save: "შენახვა",
    trash: "წაშლილი",
    inbox: "შემოსული",
    sent: "გაგზავნილი",
    drafts: "დრაფტები",
    favorites: "ფავორიტები",
    compose_message: "ახალი შეტყობინება",
    message: "ახალი შეტყობინება",
    compose: "შექმნა",
    to: "ვის",
    university: "უნივერსიტეტი",
    type_text: "შეიყვანეთ ტექსტი",
    send: "გაგზავნა",
    academic_personal: "აკადემიური პერსონალი",
    invited_personal: "მოწვეული პერსონალი",
    personal_id: "პირადი ნომერი",
    invited: "მოწვეული",
    academic: "აკადემიური",
    administration_personal: "ადმინისტრაციული პერსონალი",
    age: "ასაკი",
    personal_information: "პირადი ინფორმაცია",
    education: "განათლება",
    position: "პოზიცია",
    date_of_birth: "დაბ თარიღი",
    gender: "სქესი",
    sex: "სქესი",
    basicOfEnrollments: "ჩარიცხვის საფუძვლები",
    learnYear: "ნაკადი",
    learn_year: "სასწავლო წელი",
    country: "ქვეყანა",
    salary: "ხელფასი",
    course: "კურსი",
    working_place: "სამუშაო ადგილის დასახელება",
    department: "დეპარტამენტი",
    status: "სტატუსი",
    hours: "საათები",
    paid_hours: "ანაზღაურებადი საათები",
    unpaid_hours: "არაანაზღაურებადი საათები",
    direction: "განათლება",
    degree: "ხარისხი",
    guideline: "გზამკვლევი",
    qualification: "კვალიფიკაცია",
    category: "კატეგორია",
    additional_information: "დამატებითი ინფორმაცია",
    appointment: "დანიშვნა",
    acts: "კრედიტი",
    program: "პროგრამა",
    basic_of_enrollments: "ჩარიცხვის საფუძვლები",
    work_type_id: "სამუშაო ადგილი",
    e_doc: "ცნობები",
    prepare_e_doc: "ცნობის მომზადება",
    template: "შაბლონი",
    manual: "ინდივიდუალური",
    cancel: "გაუქმება",
    cancel_graphic: "ინდ. გრაფიკის გაუქმება",
    create_template: "შაბლონების მართვა",
    e_doc_request: "ცნობის მოთხოვნა",
    e_docs: "მოთხოვნილი ცნობები",
    accepted_docs: "მიღებული ცნობები",
    surveys: "გამოკითხვა",
    applicants: "აპლიკანტები",
    create_alert: "წარმატებით დაემატა!",
    update_alert: "წარმატებით განახლდა!",
    choose_item: "არჩევა",
    choose_date: "თარიღის არჩევა",
    attached_files: "მიმაგრებული ფაილები",
    morning: "დილა მშვიდობისა",
    afternoon: "შუადღე მშვიდობისა",
    evening: "საღამო მშვიდობისა",
    night: "ღამე მშვიდობისა",
    your_schedule: "შენი განრიგი",
    schedule: "ცხრილი",
    time_table: "ცხრილი",
    week_table: "კვირის ცხრილი",
    semester_table: "სემესტრის ცხრილი",
    check: "შემოწმება",
    yes: "კი",
    no: "არა",
    file_upload: "ატვირთეთ ფაილი",
    file_upload_short: "ფაილის ატვ.",
    others: "სხვადასხვა",
    contact_info: "საკონტაქტო ინფორმაცია",
    personal_info: "პერსონალური ინფორმაცია",
    choose_unit: "ერთეულის არჩევა",
    price: "ღირებულება",
    color: "ფერის არჩევა",
    georgian: "ქართული",
    english: "ინგლისური",
    address_ka: "მისამართი ქართულად",
    address_en: "მისამართი ინგლისურად",
    students_quantity: "სტუდენტების რაოდენობა",
    disabled_persons: "შშმ პირთა დაშვება",
    projector: "პროექტორი",
    multimedia: "მულტიმედია",
    exam_room: "საგამოცდი აუდიტორია",
    cctv: "კამერა",
    add_your_comment: "დაამატეთ კომენტარი",
    computer_lab: "კომპიუტერების ოთახი",
    lectures_another_university: "ატარებს ლექციებს სხვა უნივერსიტეტში",
    diploma_issued: "დიპლომი გაცემულია",
    diploma_issue_date: "დიპლომის გაცემის თარიღი",
    diploma_number: "დიპლომის ნომერი",
    enrollment_date: "ჩარიცხვის თარიღი",
    order_number: "ბრძანების ნომერი",
    enrollment_basis: "ჩარიცხვის საფუძვლები",
    description: "აღწერა",
    automatic: "ავტომატიზაცია",
    index: "ინდექსი",
    main_info: "ძირითადი ინფორმაცია",
    questions: "კითხვები",
    question: "კითხვა",
    type: "ტიპი",
    quest_type: "კითხვის ტიპი",
    required: "სავალდებულო",
    required_comment: "სავალდებულო კომენტარი",
    add_quest: "კითხვის დამატება",
    assessments: "შეფასებები",
    assessmets_type: "შეფასების ტიპი",
    responsibility_and_autonomy: "პასუხისმგებლობა და ავტონომია",
    knowledge_and_understanding: "ცოდნა და გაცნობიერება",
    parent: "მშობელი",
    bio: "ბიო",
    diploma_copy: "დიპლომის ასლი",
    motivation_letter: "სამოტივაციო წერილი",
    motivation_letter_dvaaa: "საერთო სამაგისტრო გამოცდებში მონაწილეობის დამადასტურებელი ბარათი",
    country: "ქვეყანა",
    country_name: "ქვეყნის დასახელება",
    local: "ადგილობრივი",
    international: "საერთაშორისო",
    date_of_vacancy_order: "ვაკანსიის გამოცხადების ბრძანების თარიღი",
    vacancy_order_number: "ვაკანსიის გამოცხადების ბრძანება",
    vacancy_document: "ვაკანსიის დოკუმენტი",
    appointment_order_number: "დანიშვნის ბრძანება",
    job_command_file: "ვაკანსიის ბრძანების ფაილი",
    date_of_appointment_order: "დანიშვნის ბრძანების თარიღი",
    appointment_order_file: "დანიშვნის ბრძანების ფაილი",
    start_of_contract: "კონტრაქტის დაწყება",
    completion_of_contract: "კონტრაქტის დასრულება",
    period_of_work: "ვადა",
    educational_activity: "საგანმანათლებო საქმიანობა",
    marital_status: "ოჯახური მდგომარეობა",
    fathers_name: "მამის სახელი",
    open_competition: "ღია კონკურსი",
    private_case: "პირადი საქმე",
    cv_georgian: "CV ქართულად",
    cv_english: "CV ინგლისურად",
    id_card: "პირადობის მოწმობა",
    diploma: "დიპლომი",
    scientific_works: "სამეცნიერო ნაშრომები",
    certificate: "ცნობა",
    view_file: "ნახვა",
    grant: "მინიჭება",
    professor: "პროფესორი",
    assistant_professor: "ასისტენტ პროფესორი",
    assistant: "ასისტენტი",
    associate_professor: "ასოცირებული პროფესორი",
    active: "აქტიური",
    inactive: "არააქტიური",
    married: "დაოჯახებული",
    single: "დასაოჯახებელი",
    amount: "თანხა",
    vacation: "შვებულება",
    engaged_in_edu_activities: "ეწევა თუ არა საგანმანათლებლო საქმიანობას",
    midterm_and_final: "შუალედური და დასკვნითი შეფასებები",
    name_of_workplace: "სამუშაო ადგილის დასახელება",
    non_administration: "დამხმარე",
    week: "კვირა",
    main_literature: "ძირითადი ლიტერატურა",
    add_literature: "დამატებითი ლიტერატურა",
    exam_date: "გამოცდის თარიღი",
    expiration_date: "დახურვის თარიღი",
    choose_subject: "საგნის არჩევა",
    subject_name: "საგნის დასახელება",
    lecture_time: "საგნის განხორციელების დრო",
    total_places: "სულ დარჩენილი ადგილები",
    free_places: "თავისუფალი ადგილები",
    similar_news: "მსგავსი სიახლეები",
    not_found: "ჩანაწერი არ მოიძებნა",
    start_year: "დაწყების წელი",
    end_year: "დასრულების წელი",
    eng_syllabus: "ინგლისური სილაბუსი",
    signature: "ხელმოწერა",
    download: "გადმოწერა",
    time: "დრო",
    lecture_type: "ლექციის ტიპი",
    database: "მონაცემთა ბაზები",
    info_links: "ინფორმაციული ლინკები",
    books_and_journals: "წიგნები და ჟურნალები",
    biology_and_ecology: "ბიოლოგია და ეკოლოგია",
    medical_journals: "სამედიცინო ჟურნალები",
    journals: "ჟურნალები",
    biology: "ბიოლოგია",
    tech_news: "ტექნოლოგიური სიახლეები",
    folow_link: "ბმულზე გადასვლა",
    total_fee: "ჯამურად გადასახდელი",
    annual_fee: "წლიური საფასური",
    grant_amount: "გრანტის ოდენობა",
    stipend: "სტიპენდია",
    additional_fee: "დამატებითი საგნების ღირებულება",
    prev_debt: "წინა წლის დავალიანება ",
    deadlines: "ვადების ცხრილი",
    current_fee: "მიმდინარე გადასახადი",
    paid_in_advance: "ავანსად გადახდილი",
    payment_date: "გადახდის თარიღი",
    to_be_paid: "გადასახდელი თანხა",
    paid_amount: "გადახდილი თანხა",
    remaining_amount: "მიმდინარე დავალიანება",
    how_to_pay: "როგორ გადავიხადო",
    bank_name: "ბანკის დასახელება",
    bank_code: "საბანკო კოდი",
    ben_name: "მიმღების დასახელება",
    ben_account: "ანგარიშსწორების ანგარიში",
    gipa_full: "ააიპ საქართველოს საზოგადოებრივ საქმეთა ინსტიტუტი",
    payer: "გადამხდელი",
    destination: "გადახდის დანიშნულება",
    application: "განცხადების დაწერა",
    application_status: "განცხადების სტატუსის ნახვა",
    anonymous_survey: "ანონიმური გამოკითხვა",
    point: "ქულა",
    commission_appointment_order: "კომისიის დანიშვნის ბრძანება",
    commission_appointment_order_en: "Commission Appointment Order",
    error: "შეცდომა",
    add_alert: "წარმატებით გაიგზავნა",
    stamp_and_signature: "სველი ხელმოწერა და ბეჭედი",
    edoc_text: "შაბლონის ტექსტი",
    comment: "კომენტარი",
    completed: "დასრულებული",
    rejected: "უარყოფილი",
    pending: "მზადდება",
    hr: "სთ",
    current_subjects: "მიმდინარე საგნები",
    discount: "ფასდაკლება",
    attachment: "მიბმული ფაილი",
    message_subject: "თემა",
    write_message: "დაწერეთ შეტყობინება",
    message_sent: "შეტყობინება გაიგზავნა",
    answer: "პასუხი",
    choose_lecturer: "აირჩიეთ ლექტორი",
    survey_error_text: "ფიფქით მონიშნული ყველა კითხვის შევსება სავალდებულოა",
    elected_alert: "გთხოვთ გააუქმოთ საგანზე რეგისტრაცია",
    reg_interval: "რეგისტრაციიის პერიოდი",
    today_list_title: "დღევანდელი ლექციების განრიგი",
    select: "არჩევა",
    loading: "მუშავდება",
    elected_filled_alert: "საგანზე ადგილები შევსებულია",
    elected_finish_alert: "საგანზე რეგისტრაცია დასრულდა",
    elected_alert_register: "საგანზე რეგისტრაცია გაიხსნება",
    spring: "საგაზაფხულო სემესტრი",
    autumn: "საშემოდგომო სემესტრი",
    survey_link: "გამოკითხვის ბმული",
    master_registration_form: "სარეგისტრაციო ფორმა (მაგისტრატურა)",
    bachelor_registration_form: "სარეგისტრაციო ფორმა (ბაკალავრიატი)",
    reports: "ანგარიშები",
    grade_analyze: "ნიშნების ანალიზი",
    agreement: "ხელშეკრულება",
    register: "რეგისტრაცია",
    from_date: "დან",
    to_date: "მდე",
    "lecturer-finances": "ლექტორის ჰონორარი",
    room_booking: "ოთახის დარეზერვება",
    download_diploma: "დიპლომის დანართის გადმოწერა",
    select_language: "აირჩიეთ ენა",
    georgian: "ქართული",
    english: "ინგლისური",
    statistics: "სტატისტიკა",
    total: "სრული რაოდენობა",
    secondary: "საშუალო",
    professional: "პროფესიული",
    doctoral: "დოქტორანტი",
    non_affiliated: "არააფილირებული",
    "minor-logs": "მაინორის არჩევა",
  },
  en: {
    admin: "Admin",
    profile: "Profile",
    edit: "Edit",
    view: "View",
    delete: "Delete",
    create: "Create",
    action: "Action",
    close: "Close",
    export: "Export",
    import: "Import",
    copy: "Copy",
    filters: "Filters",
    add: "Add",
    male: "Male",
    female: "Female",
    library: "Library",
    add_book: "Add Book",
    profile_edit: "Profile Edit",
    photo: "Photo",
    full_name: "Full Name",
    first_name: "First Name",
    last_name: "Last Name",
    name_ka: "Georgian Name",
    name_en: "English Name",
    surname_en: "English Surname",
    id_number: "ID Number",
    id_card_number: "ID Card Number",
    citizenship: "Citizenship",
    groups: "Groups",
    group: "Group",
    mobility: "Mobility",
    phone: "Phone",
    email: "Email",
    admin_position: "Administration Position",
    admin_school: "Admin School",
    title: "Title",
    author: "Author",
    lecturer: "Lecturer",
    topic: "Topic",
    release_date: "Release Date",
    subject: "Subject",
    book_upload: "Upload Book",
    logout: "Logout",
    login: "Login",
    recovery: "Recovery",
    password_recovery: "Password Recovery",
    forgot_pass: "Forgot password",
    password: "Password",
    enter_password: "Enter password",
    enter_new_password: "Enter new password",
    enter_email: "Enter email",
    with_google: "Login with email",
    language: "Language",
    syllabus: "Syllabus",
    lecture_hours: "Lecture hours",
    creating_sylabus: "Creating a syllabus",
    name_of_tr_course: "Name of the training course",
    name_of_course: "Name of the course",
    course_level: "Course level",
    bachelor: "Bachelor",
    master: "Master",
    phd: "PHD",
    tcc: "Traininig Center",
    hse: "HSE",
    english_level_id: "English level id",
    university: "University",
    course_status: "Course status",
    semester_of_course_impl: "Semester of the course implementation",
    semester: "Semester",
    credit: "Credit",
    ects_credits: "ECTS credits",
    course_lecturer:
      "Person carrying out the course/lecturer Contact information Day/days determined for individual work with the students",
    dist_of_hours: "Distribution of hours according to the load of the student",
    goals_of_course: "Goals of the academic course",
    learning_process: "Forms and activities of teaching/learning process",
    assess_components: "Assessment Components",
    eval_system: "Evaluation System",
    admission_preconditions: "Admission Preconditions",
    learning_outcomes: "Learning Outcomes",
    score: "Score",
    min_score: "Min. score",
    course_code: "Course code",
    code: "Code",
    skill: "Skill",
    assessment: "Assessment",
    responsibility_and_autonomy: "Responsibility and autonomy",
    knowledge_and_understanding: "Knowledge and understanding",
    num_of_credits: "Number of credits",
    quantity: "Quantity",
    contact_hours: "Contact Hours",
    free_hours: "Free Hours",
    seminar: "Seminar",
    topics_and_activities:
      "Academic course/module content (topics and activities)",
    recovery_missed_component: "Recovery of the missed evaluation component",
    prerequisites: "Prerequisites",
    academic_plagiarism:
      "Academic dishonesty – use of the preliminarily prepared means of cheating, cheating, helping in cheating, dictating and plagiarism",
    prerequisite_for_admission:
      "The prerequisite for admission to the final exam, the minimum competence limit for the final exam and the condition for passing the additional exam",
    Int_and_final_eval: "Intermediate and final evaluations",
    total: "Total",
    logo_title: "Georgian institute of public affairs",
    students: "Students",
    lecturers: "Lecturers",
    lecture: "Lecture",
    roles: "Roles",
    management: "Management",
    permission: "Permission",
    administration: "Administration",
    administrative: "Administrative",
    admin_positions: "Administrative positions",
    admin_units: "Administrative units",
    learning_plan: "Learning plan",
    student_groups: "Student groups",
    flows: "Flows",
    academic_years: "Academic years",
    schools: "Schools",
    school: "School",
    recommendations: "Recommendation",
    learning_programs: "Learning programs",
    learning_auditors: "Learning auditors",
    auditorium: "Auditorium",
    users: "Users",
    campuses: "Campuses",
    "library-lmb": "e-library",
    address: "Address",
    search: "Search",
    add_book: "Add book",
    topic_management: "Topic management",
    curriculum: "Curriculum",
    eval_components: "Evaluation components",
    finances: "Finances",
    concentrations: "Concentrations",
    ac_performance: "Academic performance",
    academic_degree: "Academic Degree",
    affiliated: "Academic/Invited",
    calendar: "Calendar",
    reg_form: "Registration form",
    quality_service: "Quality service",
    table: "Table",
    analysis_of_signs: "Analysis of signs",
    silabus_title: "New Sylabus",
    direct_message: "Direct message",
    journal: "Journal",
    day: "Week Day",
    sunday: "Sunday",
    monday: "Monday",
    tuesday: "Tuesday",
    wednesday: "Wednesday",
    thursday: "Thursday",
    friday: "Friday",
    saturday: "Saturday",
    sun: "Sun",
    mon: "Mon",
    tu: "Tu",
    wed: "Wed",
    thu: "Thu",
    fri: "Fri",
    sat: "Sat",
    january: "January",
    february: "February",
    march: "March",
    april: "April",
    may: "May",
    june: "June",
    july: "July",
    august: "August",
    september: "September",
    october: "October",
    november: "November",
    december: "December",
    main: "Main",
    table: "Table",
    lists: "Lists",
    messages: "Messages",
    payment: "Payment",
    req_sent: "Request has been sent",
    statement: "Statements",
    news: "News",
    about_gipa: "About GIPA",
    radio_gipa: "Radio GIPA",
    trainings: "Trainings",
    my_subjects: "My subjects",
    academic_year: "2024-2025 academic year",
    past_subjects: "Passed subjects",
    career: "Career",
    projects: "Projects",
    vacancies: "Vacancies",
    jstor_base: "EBSCO & JSTOR ბაზები",
    proxy_config: "Proxy configs",
    book_search: "Search",
    change_password: "Change password",
    current_password: "Current password",
    new_password: "New password",
    confirm_new_password: "Confirm new password",
    save: "Save",
    trash: "Trash",
    inbox: "Inbox",
    sent: "Sent",
    drafts: "Drafts",
    favorites: "Favorites",
    compose_message: "Compose Message",
    message: "Message",
    compose: "Compose",
    to: "To",
    subject: "Subject",
    type_text: "Type your text here",
    send: "Send",
    academic_personal: "Academic personal",
    invited: "Invited",
    academic: "Academic",
    invited_personal: "Invited personal",
    personal_id: "Personal ID",
    administration_personal: "Administration personal",
    age: "Age",
    personal_information: "Personal Information",
    education: "Education",
    position: "Position",
    date_of_birth: "Date of Birth",
    gender: "Gender",
    sex: "Sex",
    basicOfEnrollments: "Enrollment Type",
    learnYear: "Flow",
    learn_year: "Learn Year",
    country: "Country",
    salary: "Salary",
    course: "Course",
    working_place: "Working Place",
    department: "Department",
    status: "Status",
    hours: "Hours",
    paid_hours: "Paid Hours",
    unpaid_hours: "Unpaid Hours",
    direction: "Education",
    degree: "Degree",
    guideline: "Guideline",
    qualification: "Qualification",
    category: "Category",
    additional_information: "Additional Information",
    appointment: "Appointment",
    acts: "Act",
    program: "Program",
    work_type_id: "Work Type",
    e_doc: "edoc",
    prepare_e_doc: "Prepare e-doc",
    template: "Template",
    manual: "Manual",
    check: "Check",
    cancel: "Cancel",
    cancel_graphic: "Cancel ind. Graphic",
    create_template: "Management",
    e_doc_request: "Request",
    e_docs: "Requested Docs",
    accepted_docs: "Accepted Docs",
    surveys: "Surveys",
    applicants: "Applicants",
    create_alert: "Added successfully!",
    update_alert: "Successfully updated!",
    choose_item: "Choose Item",
    choose_date: "Choose Date",
    attached_files: "Attached Files",
    morning: "Good morning",
    afternoon: "Good Afternoon",
    evening: "Good Evening",
    night: "Good Night",
    your_schedule: "Your schedule",
    schedule: "Schedule",
    time_table: "Time Table",
    week_table: "Week Table",
    semester_table: "Semester Table",
    yes: "Yes",
    no: "No",
    file_upload: "File Upload",
    file_upload_short: "File Upload",
    others: "Others",
    contact_info: "Contact Information",
    personal_info: "Personal Information",
    choose_unit: "Choose Unit",
    price: "Price",
    color: "Color",
    address_ka: "Address Georgian",
    georgian: "Georgian",
    english: "English",
    address_en: "Address English",
    students_quantity: "Students Quantity",
    disabled_persons: "Disabled Persons",
    projector: "Projector",
    multimedia: "Multimedia",
    exam_room: "Exam Room",
    cctv: "cctv",
    add_your_comment: "Add your comment",
    computer_lab: "Computer Lab",
    lectures_another_university: "Gives lectures at another university",
    diploma_issued: "The diploma has been issued",
    diploma_issue_date: "Diploma issue date",
    diploma_number: "Diploma number",
    description: "Description",
    automatic: "Automatic",
    index: "Index",
    main_info: "Main Information",
    questions: "Questions",
    question: "Question",
    type: "type",
    quest_type: "Question Type",
    required: "Required",
    required_comment: "Comment is required",
    add_quest: "Add Question",
    assessments: "Assessments",
    assessmets_type: "Assessment Type",
    parent: "Parent",
    enrollment_date: "Enrollment Date",
    order_number: "Order Number",
    enrollment_basis: "Enrollment Basis",
    bio: "Bio",
    diploma_copy: "Diploma Copy",
    motivation_letter: "Motivation Letter",
    motivation_letter_dvaaa: "General Master's Exam Participation Certificate",
    country: "Country",
    country_name: "Country Name",
    local: "Local",
    international: "International",
    date_of_vacancy_order: "Date of the vacancy announcement order",
    vacancy_order_number: "vacancy announcement order number",
    appointment_order_number: "Appointment order number",
    job_command_file: "Job command file",
    date_of_appointment_order: "Date of appointment order",
    appointment_order_file: "Appointment order file",
    start_of_contract: "Start of contract",
    completion_of_contract: "Completion of the contract",
    period_of_work: "Duration",
    educational_activity: "Educational activity",
    marital_status: "Marital status",
    fathers_name: "Father's name",
    open_competition: "Open Competition",
    private_case: "Private Case",
    cv_georgian: "CV in Georgian",
    cv_english: "CV in English",
    id_card: "ID Card",
    diploma: "Diploma",
    scientific_works: "Scientific Works",
    certificate: "Certificate",
    view_file: "View",
    grant: "Grant",
    professor: "Professor",
    assistant_professor: "Assistant Professor",
    assistant: "Assistant",
    associate_professor: "Associate Professor",
    active: "Active",
    inactive: "Inactive",
    married: "Married",
    single: "Single",
    amount: "Amount",
    vacation: "Vacation",
    engaged_in_edu_activities: "Is engaged in educational activities?",
    midterm_and_final: "Midterm and final exams",
    name_of_workplace: "Name of workplace",
    non_administration: "Non administration",
    main_literature: "Main Literature",
    add_literature: "Additional Literature",
    week: "Week",
    exam_date: "Exam Date",
    expiration_date: "Expiration Date",
    choose_subject: "Choose Subject",
    subject_name: "Subject",
    lecture_time: "Lecture Time",
    total_places: "Total Places",
    free_places: "Free Places",
    similar_news: "Similar News",
    not_found: "No data found",
    start_year: "Start Year",
    end_year: "End Year",
    eng_syllabus: "English Syllabus",
    signature: "Signature",
    download: "Download",
    time: "Time",
    lecture_type: "Lecture Type",
    database: "Database",
    info_links: "Information Links",
    books_and_journals: "Books and Journals",
    biology_and_ecology: "Biology and Ecology",
    medical_journals: "Medical Journals",
    journals: "Journals",
    biology: "Biology",
    tech_news: "Technological News",
    folow_link: "Follow the link",
    total_fee: "Total Fee",
    annual_fee: "Annual tuition fee",
    grant_amount: "Amount of grant",
    stipend: "Stipend",
    additional_fee: "Fee of additional subjects",
    prev_debt: "Previous year debt",
    deadlines: "Deadlines",
    current_fee: "Current Fee",
    paid_in_advance: "Paid in Advance",
    payment_date: "Date of the Payment",
    to_be_paid: "Amount to be Paid",
    paid_amount: "Amount Paid",
    remaining_amount: "Remaining Amount",
    how_to_pay: "How to pay",
    bank_name: "Bank name",
    bank_code: "Bank code",
    ben_name: "NAME OF BENEFICIARY",
    gipa_full: "NNLE “GEORGIAN INSTITUTE OF PUBLIC AFFAIRS”",
    ben_account: "Ben's account",
    payer: "Payer",
    destination: "Destination",
    application: "Application",
    application_status: "Application Status",
    anonymous_survey: "Anonymous Survey",
    point: "Point",
    commission_appointment_order: "Commission Appointment Order",
    error: "Error",
    add_alert: "Sent Successfuly",
    stamp_and_signature: "Stamp and Signature",
    edoc_text: "Edoc text",
    comment: "Comment",
    completed: "Completed",
    rejected: "Rejected",
    pending: "Pending",
    hr: "hr",
    current_subjects: "Current Subjects",
    discount: "Discount",
    attachment: "Attachment",
    message_subject: "Subject",
    write_message: "Write a message",
    message_sent: "Message has been sent",
    answer: "Answer",
    choose_lecturer: "Choose lecturer",
    survey_error_text: "All questions marked with an asterisk are required",
    elected_alert: "Please remove the current registration",
    reg_interval: "Registration Time",
    today_list_title: "დღევანდელი ლექციების განრიგი",
    select: "Select",
    loading: "Loading",
    elected_filled_alert: "The places on the subject are filled",
    elected_finish_alert: "Registration for the subject has been completed",
    elected_alert_register: "Registration will start at",
    spring: "Spring semester",
    autumn: "Autumn semester",
    past_subjects: "Past subjects",
    point_marker: "Point Mark",
    survey_link: "Survey link",
    master_registration_form: "Registration form (Master)",
    bachelor_registration_form: "Registration form (Bachelor)",
    reports: "Reports",
    grade_analyze: "Signs analyze",
    agreement: "Agreement",
    register: "Register",
    from_date: "From",
    to_date: "To",
    "lecturer-finances": "Lecturer fee",
    room_booking: "Room booking",
    download_diploma: "Download Diploma",
    select_language: "Select diploma language",
    georgian: "Georgian",
    english: "English",
    statistics: "Statistics",
    total: "Total",
    secondary: "Secondary",
    professional: "Professional",
    doctoral: "Doctoral Student",
    non_affiliated: "Non-affiliated",
    "minor-logs": "Minor Selection",
  },
};
