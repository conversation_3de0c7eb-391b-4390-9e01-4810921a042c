export const lecturersFields = [
    {
        groupHeading: 'პეროსონალური ინფორმაცია'
    },
    {
        name: 'first_name',
        type: 'text',
        placeholder: 'სახელი',
        inputType: 'input',
        label: 'სახელი',
        id: 'first_name',
        required: true
    },
    {
        name: 'last_name',
        type: 'text',
        placeholder: 'გვარი',
        inputType: 'input',
        label: 'გვარი',
        id: 'last_name',
        required: true
    },
    {
        name: 'identity_number',
        type: 'text',
        placeholder: 'პირადი ნომერი',
        inputType: 'input',
        label: 'პირადი ნომერი',
        id: 'identity_number',
        required: true
    },
    {
        name: 'card_number',
        type: 'text',
        placeholder: 'პირადობის N',
        inputType: 'input',
        label: 'პირადობის N',
        id: 'card_number',
    },
    {
        name: 'address',
        type: 'text',
        placeholder: 'მისამართი',
        inputType: 'input',
        label: 'მისამართი',
        id: 'address',
    },
    {
        name: 'date_of_birth',
        type: 'date',
        placeholder: 'დაბადების თარიღი',
        inputType: 'input',
        label: 'დაბადების თარიღი',
        id: 'date_of_birth',
    },
    {
        groupHeading: 'საკონტაქტო ინფორმაცია'
    },
    {
        name: 'phone',
        type: 'tel',
        placeholder: '123-45-678',
        pattern: '[0-9]{3}-[0-9]{2}-[0-9]{3}',
        inputType: 'input',
        label: 'ტელეფონი',
        id: 'phone',
        required: true
    },
    {
        name: 'email',
        type: 'email',
        placeholder: 'ელ ფოსტა',
        inputType: 'input',
        label: 'ელ ფოსტა',
        id: 'email',
        required: true
    },
    {
        groupHeading: 'მიმაგრებული ფაილები'
    },
    {
        name: 'photo',
        type: 'file',
        placeholder: 'ფოტო',
        inputType: 'image',
        label: 'ფოტო',
        id: 'photo',
    },
    {
        name: 'cv',
        type: 'file',
        placeholder: 'cv',
        inputType: 'file',
        label: 'cv',
        id: 'cv',
    },
    {
        groupHeading: 'სხვადასხვა'
    },
    {
        name: 'academic_degree_id',
        type: 'select',
        placeholder: 'აკადემიური ხარისხი',
        inputType: 'select',
        label: 'აკადემიური ხარისხი',
        id: 'academic_degree_id',
        relation: 'academic_degree'
    },
    // {
    //     name: 'type',
    //     type: 'select',
    //     placeholder: 'ტიპი',
    //     inputType: 'select',
    //     label: 'ტიპი',
    //     id: 'type',
    //     relation: 'school'
    // },
    {
        name: 'directions_id',
        type: 'select',
        placeholder: 'განათლება',
        inputType: 'select',
        label: 'განათლება',
        id: 'type',
        relation: 'direction'
    },
    {
        name: 'do_lectures_another_universities',
        type: 'checkbox',
        inputType: 'checkbox',
        label: 'ატარებს ლექციებს სხვა უნივერსიტეტში',
        id: 'do_lectures_another_universities',
    },
    {
        name: 'affiliated',
        type: 'checkbox',
        inputType: 'checkbox',
        label: 'აფილირებული',
        id: 'affiliated',
    },
]